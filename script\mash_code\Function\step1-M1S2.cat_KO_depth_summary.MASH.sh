#!/bin/bash
:<<!EOF!
 * @Date: 2023-06-14 16:58:11
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-08-18 15:41:58
 * @FilePath: /liliuyang/R/Rscript.MASH/202308-v2/M1S2.cat_KO_depth_summary.MASH.sh
 * @Description:
!EOF!

help_message () {
	echo ""
	echo "Usage: M1S2.cat_KO_depth_summary.sh [options] -l list_sample -i input_DIR -s script_DIR -p A001 -o output_DIR"
	echo "Note1: Make sure to provide directory and file prefix."
	echo ""
	echo "Options:"
	echo ""
	echo "	-l STR          file for sample list"
	echo "	-i STR          directory for input_dir"
	echo "	-s STR          directory for script"
	echo "	-p STR          prefix for each batch of sample list"
	echo "	-o STR          directory for output and error files (if exist). e.g., prefix.KO_Sample.wide.tsv, prefix.error_KO_sum.tsv"
	echo "";}

########################################################################################################
########################               LOADING IN THE PARAMETERS                ########################
########################################################################################################
#
## load in params
OPTS=`getopt -o hl:i:s:p:o: --long list_sample:,input_DIR:,script_DIR:,prefix:,output_DIR:,help -- "$@"`
if [ $? -ne 0 ]; then help_message; exit 1; fi
# loop through input params
while true; do
		case "$1" in
				-l) list_sample=$2; shift 2;;
				-i) input_DIR=$2; shift 2;;
				-s) script_DIR=$2; shift 2;;
				-p) prefix=$2; shift 2;;
				-o) output_DIR=$2; shift 2;;
				-h | --help) help_message; exit 1; shift 1;;
				--) help_message; exit 1; shift; break;;
				*) break;;
		esac
done

########################################################################################################
########################           MAKING SURE EVERYTHING IS SET UP             ########################
########################################################################################################


#check if the prefix exists
if [ -z $prefix ]; then
	echo "Please input the prefix. Exiting..."
	help_message; exit 1
fi


#check if the list_sample was input
if [ -z ${list_sample} ]; then
	echo "Please input the list of sample."
	help_message; exit 1
else
	#check if the list_sample file exists
	if [ ! -s $list_sample ]; then
			echo "$list_sample does not exist. Exiting..."
			help_message; exit 1
	fi
fi


#check if the input_DIR dir exists
if [ -z ${input_DIR} ]; then
	echo "Please input the input_DIR."
	help_message; exit 1
else
	if [ ! -d $input_DIR ]; then
			echo "Directory of $input_DIR does not exist. Exiting..."
			help_message; exit 1
	fi
fi


# Checks for scripts folder
if [ -z ${script_DIR} ]; then
	echo "Please input the script_DIR."
	help_message; exit 1
else
	if [ ! -s ${script_DIR}/M1S2.KO.depth.merge.cmd.R ]; then
			echo "The file ${script_DIR}/M1S2.KO.depth.merge.cmd.R does not exist."
			help_message; exit 1
	fi
fi


#check if the output_DIR dir exists
if [ -z ${output_DIR} ]; then
	echo "Please input the output_DIR."
	help_message; exit 1
fi
########################################################################################################
########################                    BEGIN PIPELINE!                     ########################
########################################################################################################

eval "$(conda shell.bash hook)"
conda activate R4.2


output_KO_Sample_long=${output_DIR}/AAA_KO_sum/${prefix}.KO_Sample.long.tsv
output_KO_Sample_wide=${output_DIR}/AAA_KO_sum/${prefix}.KO_Sample.wide.tsv
output_error_KO_Sample=${output_DIR}/tmp_KO_sum/${prefix}.error_KO_sum.tsv


if [ ! -d ${output_DIR} ]; then mkdir -p ${output_DIR};
else
	echo "Warning: ${output_DIR} already exists."
fi

if [ ! -d ${output_DIR}/AAA_KO_sum/ ]; then mkdir -p ${output_DIR}/AAA_KO_sum/;
else
    echo "Warning: ${output_DIR}/AAA_KO_sum/ already exists."
    if [ -f ${output_KO_Sample_long} ]; then rm -r ${output_KO_Sample_long}; fi
    if [ -f ${output_KO_Sample_wide} ]; then rm -r ${output_KO_Sample_wide}; fi
fi

if [ ! -d ${output_DIR}/tmp_KO_sum/ ]; then mkdir -p ${output_DIR}/tmp_KO_sum/;
else
    echo "Warning: ${output_DIR}/tmp_KO_sum/ already exists."
    if [ -f ${output_error_KO_Sample} ]; then rm -r ${output_error_KO_Sample}; fi
fi

touch ${output_KO_Sample_long}
# 输出结果
for sample in `cat ${list_sample}`
do
    if [ ! -s ${input_DIR}/${sample}.KO_depth.tsv ]; then
        echo "${input_DIR}/${sample}.KO_depth.tsv does not exist. Skiping ${sample}..."
    else
        cat ${input_DIR}/${sample}.KO_depth.tsv >> ${output_KO_Sample_long}
    fi
done

sed -i "s|^|MASH_|" ${output_KO_Sample_long}

if [ -s ${output_KO_Sample_long} ]; then
    Rscript ${script_DIR}/M1S2.KO.depth.merge.cmd.R \
        --in_KO_Sample_long ${output_KO_Sample_long} \
        --out_KO_Sample_wide ${output_KO_Sample_wide}
else
    touch ${output_error_KO_Sample}
fi



########################################################################################################
########################      MERGE KO AND ABUNDANCE PIPELINE SUCCESSFULLY FINISHED!!!         ########################
########################################################################################################
echo "MERGE KO AND ABUNDANCE PIPELINE SUCCESSFULLY FINISHED!!!"
