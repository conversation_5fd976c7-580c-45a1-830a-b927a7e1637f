suppressMessages(library(GetoptLong))
suppressMessages(library(pheatmap))
suppressMessages(library(tidyverse))

rm(list = ls(all.names = TRUE))

# 参数
GetoptLong(
  "log10_file=s",       "log10表达矩阵文件",
  "average_file=s",     "average表达矩阵文件",
  "Group_file=s",       "分组文件，需包含sample_name和Group两列",
  "diffKO_file=s",      "差异KO列表csv，至少包含KO列和Type列",
  "out_DIR=s",          "输出目录",
  "prefix=s",           "输出文件名前缀",
  "verbose!",           "打印详细信息"
)

if (verbose) {
  cat("参数信息:\n")
  cat("log10_file:", log10_file, "\n")
  cat("average_file:", average_file, "\n")
  cat("Group_file:", Group_file, "\n")
  cat("diffKO_file:", diffKO_file, "\n")
  cat("out_DIR:", out_DIR, "\n")
  cat("prefix:", prefix, "\n")
}

if (!dir.exists(out_DIR)) dir.create(out_DIR, recursive = TRUE)

# 读取数据
log10_mat <- read.delim(log10_file, check.names=FALSE, row.names=1, sep="\t", stringsAsFactors=FALSE)
avg_mat <- read.delim(average_file, check.names=FALSE, row.names=1, sep="\t", stringsAsFactors=FALSE)
group_df <- read.delim(Group_file, sep="\t", stringsAsFactors=FALSE)

colnames(group_df) <- tolower(colnames(group_df))
if (!all(c("sample_name", "group") %in% colnames(group_df))) {
  stop("Group_file必须包含sample_name和group两列")
}
group_df <- group_df %>% dplyr::rename(sample_name = sample_name, Group = group)

# 样本名标准化
clean_colnames <- function(cols) {
  gsub("^.*?_", "", cols)
}
colnames(log10_mat) <- clean_colnames(colnames(log10_mat))
colnames(avg_mat) <- clean_colnames(colnames(avg_mat))

# 样本名对齐
common_samples <- intersect(group_df$sample_name, colnames(log10_mat))
if(length(common_samples) == 0) stop("没有匹配的样本名")
group_df <- group_df %>% filter(sample_name %in% common_samples)
log10_mat <- log10_mat[, common_samples, drop=FALSE]
avg_mat <- avg_mat[, common_samples, drop=FALSE]

# 差异KO列表
diffKO_df <- read.csv(diffKO_file, stringsAsFactors=FALSE)
if(!all(c("KO", "Type") %in% colnames(diffKO_df))){
  stop("diffKO_file 必须包含 KO 和 Type 两列")
}
diff_KOs <- diffKO_df$KO

# 筛选差异 KO 行
diffKO_log10_mat <- log10_mat[rownames(log10_mat) %in% diff_KOs, , drop=FALSE]
diffKO_avg_mat <- avg_mat[rownames(avg_mat) %in% diff_KOs, , drop=FALSE]

if(verbose){
  cat("提取的差异KO数量（log10）:", nrow(diffKO_log10_mat), "\n")
  cat("提取的差异KO数量（average）:", nrow(diffKO_avg_mat), "\n")
}

# 设置列注释
annotation_col <- data.frame(Group = factor(group_df$Group))
rownames(annotation_col) <- group_df$sample_name

group_levels <- levels(annotation_col$Group)
color_palette <- c("#377EB8", "#FF7F00", "#E41A1C", "#4DAF4A", "#984EA3")
color_palette <- color_palette[1:length(group_levels)]
names(color_palette) <- group_levels

annotation_colors <- list(Group = color_palette)

# 色带：红-白-蓝
colors_heatmap <- colorRampPalette(c("#4575B4", "white", "#D73027"))(100)

# 绘图函数（聚类行，字体染色，自动设置中点，添加行注释）
draw_heatmap <- function(mat, diffKO_df, out_png, out_pdf, title) {
  if (verbose) cat("绘制热图:", title, "\n")

  if (nrow(mat) == 0 || ncol(mat) == 0) {
    warning("矩阵为空，跳过绘图: ", title)
    return()
  }

  mat_KOs <- rownames(mat)
  type_map <- diffKO_df %>% filter(KO %in% mat_KOs) %>% select(KO, Type) %>% deframe()

  # 行注释文字标签
  type_label <- ifelse(type_map[mat_KOs] == "selected", "Selected KO", "Other")
  annotation_row <- data.frame(Type = factor(type_label, levels = c("Selected KO", "Other")))
  rownames(annotation_row) <- mat_KOs

  # 行注释颜色
  annotation_colors_row <- list(Type = c("Selected KO" = "#377EB8", "Other" = "grey70"))

  # 行名字体颜色
  font_colors <- ifelse(type_map[mat_KOs] == "selected", "#377EB8", "grey50")
  names(font_colors) <- mat_KOs

  # 设置 breaks 中点为中位数
  all_vals <- as.vector(as.matrix(mat))
  mid_val <- median(all_vals, na.rm = TRUE)
  min_val <- min(all_vals, na.rm = TRUE)
  max_val <- max(all_vals, na.rm = TRUE)

  if (min_val == max_val) {
    warning("热图跳过绘制，因所有值相同: ", min_val)
    return()
  }

  breaks <- seq(min_val, max_val, length.out = length(colors_heatmap) + 1)

  p <- tryCatch({
    pheatmap(mat,
             annotation_col = annotation_col,
             annotation_row = annotation_row,
             annotation_colors = c(annotation_colors, annotation_colors_row),
             color = colors_heatmap,
             breaks = breaks,
             cluster_rows = TRUE,
             cluster_cols = FALSE,
             show_rownames = TRUE,
             show_colnames = TRUE,
             cellwidth = 6,
             cellheight = 6,
             main = title,
             fontsize_row = 5,
             fontsize_col = 5,
             angle_col = 45,
             labels_row = mat_KOs,
             labels_row_color = font_colors,
             silent = TRUE)
  }, error = function(e){
    message("绘图错误: ", e)
    return(NULL)
  })

  if (!is.null(p)) {
    png(file = out_png, width = 8, height = 6, units = "in", res = 150)
    print(p)
    dev.off()

    pdf(file = out_pdf, width = 8, height = 6)
    print(p)
    dev.off()
  }
}

# 输出路径
out_png_log10 <- file.path(out_DIR, paste0(prefix, "_log10_heatmap.png"))
out_pdf_log10 <- file.path(out_DIR, paste0(prefix, "_log10_heatmap.pdf"))
out_png_avg <- file.path(out_DIR, paste0(prefix, "_average_heatmap.png"))
out_pdf_avg <- file.path(out_DIR, paste0(prefix, "_average_heatmap.pdf"))


# 添加这部分：导出输入数据
write.csv(diffKO_log10_mat, file = file.path(out_DIR, paste0(prefix, "_log10_heatmap_input.csv")))
write.csv(diffKO_avg_mat, file = file.path(out_DIR, paste0(prefix, "_average_heatmap_input.csv")))

# 调用绘图
draw_heatmap(diffKO_log10_mat, diffKO_df, out_png_log10, out_pdf_log10, "Top 30 KO log transformed abundance")
draw_heatmap(diffKO_avg_mat, diffKO_df, out_png_avg, out_pdf_avg, "Top 30 KO average transformed abundance")

if(verbose) cat("Step4-KO_heatmap.R 运行完成\n")

