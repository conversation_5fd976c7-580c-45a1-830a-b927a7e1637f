package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.dto.BiogeographyResultQueryDTO;
import org.biosino.lf.mash.mashweb.dto.ChartParamDTO;
import org.biosino.lf.mash.mashweb.dto.MapDataDTO;
import org.biosino.lf.mash.mashweb.dto.MapDataInfoDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesRepository;
import org.biosino.lf.mash.mashweb.util.MathUtils;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2025/8/1
 */
@Service
public class AnalysisBaseService {

    @Autowired
    private SamplesRepository samplesRepository;

    @Autowired
    private FileProperties fileProperties;

    public final static String[] GROUP_COLOR = {
            "#377EB8",
            "#FF7F00",
            "#E41A1C",
            "#4DAF4A",
            "#984EA3"
    };

    // 根据id 查询结果数据
    public Page<SamplesBioGeographyVO> getTableResult(BiogeographyResultQueryDTO queryDTO) {
        // 读取结果文件中的所有run_list
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getTaskId()));
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(queryDTO.getTaskId()));
        File file;
        CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
        if (StrUtil.isNotBlank(queryDTO.getSelect())) {
            file = FileUtil.file(outputDir, queryDTO.getSelect() + "_result.csv");
        } else {
            file = FileUtil.file(inputDir, "Group.txt");
            csvReadConfig.setFieldSeparator('\t');
        }
        if (!file.exists()) {
            return new PageImpl<>(new ArrayList<>(), queryDTO.getPageable(), 0);
        }

        CsvReader reader = CsvUtil.getReader(csvReadConfig);
        List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);

        // 使用hutool进行内存分页
        Pageable pageable = queryDTO.getPageable();
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), list.size());

        List<SamplesBioGeographyVO> pageContent = CollUtil.sub(list, start, end);

        // 查询其他的数据
        List<String> runIds = pageContent.stream().map(SamplesBioGeographyVO::getRunId).toList();
        Map<String, Samples> runIdToSampleMap = samplesRepository.findByRunIdIn(runIds).stream().collect(Collectors.toMap(Samples::getRunId, Function.identity(), (x, y) -> y));

        List<SamplesBioGeographyVO> result = pageContent.stream().peek(x -> {
            if (runIdToSampleMap.containsKey(x.getRunId())) {
                Samples item = runIdToSampleMap.get(x.getRunId());
                BeanUtil.copyProperties(item, x);
            }
        }).toList();

        return new PageImpl<>(result, pageable, list.size());
    }

    public MapDataInfoDTO getMapData(BiogeographyResultQueryDTO queryDTO) {
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getTaskId()));
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(queryDTO.getTaskId()));

        File file = FileUtil.file(outputDir, queryDTO.getSelect() + "_result.csv");

        if (!file.exists()) {
            return null;
        }

        File sampleListFile = FileUtil.file(inputDir, "sample_list.txt");
        List<String> sampleList = FileUtil.readUtf8Lines(sampleListFile);


        CsvReader reader = CsvUtil.getReader();
        List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);
        Map<String, List<SamplesBioGeographyVO>> collect = list.stream()
                .collect(Collectors.groupingBy(x -> (x.getLongitude() != null ? x.getLongitude() : "-") + StrPool.TAB + (x.getLatitude() != null ? x.getLatitude() : "-")));

        List<MapDataDTO> mapData = new ArrayList<>();
        collect.forEach((k, v) -> {
            String[] split = k.split(StrPool.TAB);
            String longitude = split[0];
            String latitude = split[1];
            List<SamplesBioGeographyVO> dtos = v.stream().toList();
            mapData.add(new MapDataDTO(!longitude.equals("-") ? Double.parseDouble(longitude) : null,
                    !latitude.equals("-") ? Double.parseDouble(latitude) : null, dtos.stream().map(SamplesBioGeographyVO::getValue).max(Comparator.comparing(Double::doubleValue)).orElse(0.0), v.size()));
        });
        MapDataInfoDTO result = new MapDataInfoDTO();
        // 求所有的 Min Medium Max
        List<Double> avgData = list.stream().map(x -> x.getValue() / 10000).sorted(Comparator.comparingDouble(Double::doubleValue)).toList();
        double min = avgData.get(0);
        double max = avgData.get(avgData.size() - 1);
        double median = MathUtils.calculateMedian(avgData);
        result.setMapData(mapData);
        result.setMin(String.valueOf(min));
        result.setMax(String.valueOf(max));
        result.setMedian(String.valueOf(median));

        result.setSelectedNum(sampleList.size());
        result.setDetectedNum(list.size());
        return result;
    }

    public File getChartFile(ChartParamDTO queryDTO) {
        String taskId = queryDTO.getTaskId();
        String domain = queryDTO.getDomain();
        String level = queryDTO.getLevel();
        String betaMethod = queryDTO.getBetaMethod();
        String group = queryDTO.getGroup();
        String type = queryDTO.getType();

        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File file = null;


        switch (queryDTO.getChartNo()) {
            case 1:
                file = FileUtil.file(outputDir, "Barplot_Figure",
                        StrUtil.format("Barplot.{}.{}.pdf", domain, level));
                break;
            case 2:
                file = FileUtil.file(outputDir, "Lefse_Figure", StrUtil.format("Lefse.{}.{}.pdf", domain, level));
                break;
            case 3:
                file = FileUtil.file(outputDir, "Treemap_Figure",
                        StrUtil.format("Treemap_{}_Top14.pdf", group));
                break;
            case 4:
                file = FileUtil.file(outputDir, "PCoA_Figure",
                        StrUtil.format("PCoA.{}.{}.{}.pdf", domain, level, betaMethod));
                break;
            case 5:
                file = FileUtil.file(outputDir, "Function_Heatmap_Figure",
                        StrUtil.format("top30_KO_KO_{}_heatmap.pdf", type));
                break;
            default:
                break;
        }
        return file;
    }

}
