package org.biosino.lf.mash.mashweb.mongo.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.dto.HomeMapQueryDTO;
import org.biosino.lf.mash.mashweb.dto.SamplesQueryDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2025/4/10
 */
@RequiredArgsConstructor
public class SamplesCustomRepositoryImpl implements SamplesCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<Samples> queryPage(SamplesQueryDTO queryDTO) {
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);
        Query query = new Query();
        query.addCriteria(criteria);

        // 查询数据量
        long total = mongoTemplate.count(query, Samples.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Samples> content = mongoTemplate.find(query, Samples.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    private static Criteria getCriteriaFromQueryDTO(SamplesQueryDTO queryDTO) {
        ArrayList<Criteria> conditionList = new ArrayList<>();

        // 处理特定字段查询
        if (StrUtil.isNotBlank(queryDTO.getKeyword())) {
            if (StrUtil.isNotBlank(queryDTO.getField())) {
                conditionList.add(Criteria.where(queryDTO.getField()).is(queryDTO.getKeyword()));
            } else {
                Pattern pattern = Pattern.compile("^.*" + queryDTO.getKeyword() + ".*$", Pattern.CASE_INSENSITIVE);
                conditionList.add(new Criteria().orOperator(
                        Criteria.where("bio_project_id").regex(pattern),
                        Criteria.where("experiment_id").regex(pattern),
                        Criteria.where("country").regex(pattern),
                        Criteria.where("hydrosphere_type").regex(pattern),
                        Criteria.where("water_body_type_by_classification").regex(pattern),
                        Criteria.where("water_body_type_by_geographic").regex(pattern),
                        Criteria.where("water_body_name").regex(pattern)
                ));
            }
        }

        if (CollUtil.isNotEmpty(queryDTO.getAllIds())) {
            conditionList.add(new Criteria().orOperator(
                    Criteria.where("run_id").in(queryDTO.getAllIds()),
                    Criteria.where("bio_project_id").in(queryDTO.getAllIds()),
                    Criteria.where("bio_sample_id").in(queryDTO.getAllIds()),
                    Criteria.where("experiment_id").in(queryDTO.getAllIds())
            ));
        }
        if (StrUtil.isNotBlank(queryDTO.getDataset())) {
            conditionList.add(Criteria.where(queryDTO.getDataset()).is("IN"));
        }

        if (StrUtil.isNotBlank(queryDTO.getRunId())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getRunId() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("run_id").regex(pattern));
        }

        // 添加BioProject ID模糊查询
        if (StrUtil.isNotBlank(queryDTO.getBioProjectId())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getBioProjectId() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("bio_project_id").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getBioSampleId())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getBioSampleId() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("bio_sample_id").regex(pattern));
        }


        // 添加Experiment ID模糊查询
        if (StrUtil.isNotBlank(queryDTO.getExperimentId())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getExperimentId() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("experiment_id").regex(pattern));
        }

        // 添加Omics Type模糊查询
        if (StrUtil.isNotBlank(queryDTO.getOmicsType())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getOmicsType() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("omics_type").regex(pattern));
        }

        // 添加Technology Type模糊查询
        if (StrUtil.isNotBlank(queryDTO.getTechnologyType())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getTechnologyType() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("technology_type").regex(pattern));
        }

        // 添加Data Source模糊查询
        if (StrUtil.isNotBlank(queryDTO.getDataSource())) {
            conditionList.add(Criteria.where("data_source").is(queryDTO.getDataSource()));
        }

        // 添加Organism模糊查询
        if (StrUtil.isNotBlank(queryDTO.getOrganism())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getOrganism() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("organism").regex(pattern));
        }

        // 添加Geographic Location Name模糊查询
        if (StrUtil.isNotBlank(queryDTO.getGeoLocName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getGeoLocName() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("geo_loc_name").regex(pattern));
        }

        // 添加Temperature模糊查询
        if (StrUtil.isNotBlank(queryDTO.getTemperature())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getTemperature() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("temperature").regex(pattern));
        }

        // 添加Salinity模糊查询
        if (StrUtil.isNotBlank(queryDTO.getSalinity())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getSalinity() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("salinity").regex(pattern));
        }

        // 添加Depth模糊查询
        if (StrUtil.isNotBlank(queryDTO.getDepth())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getDepth() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("depth").regex(pattern));
        }

        // 添加Pressure模糊查询
        if (StrUtil.isNotBlank(queryDTO.getPressure())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getPressure() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("pressure").regex(pattern));
        }

        // 添加pH模糊查询
        if (StrUtil.isNotBlank(queryDTO.getPh())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getPh() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("ph").regex(pattern));
        }

        // 添加温度范围查询
        if (queryDTO.getTemperatureStart() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("temperature_start").gte(queryDTO.getTemperatureStart()),
                            Criteria.where("temperature_end").gte(queryDTO.getTemperatureStart())
                    )
            );
        }
        if (queryDTO.getTemperatureEnd() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("temperature_start").lte(queryDTO.getTemperatureEnd()),
                            Criteria.where("temperature_end").lte(queryDTO.getTemperatureEnd())
                    )
            );
        }

        // 添加盐度范围查询
        if (queryDTO.getSalinityStart() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("salinity_start").gte(queryDTO.getSalinityStart()),
                            Criteria.where("salinity_end").gte(queryDTO.getSalinityStart())
                    )
            );
        }
        if (queryDTO.getSalinityEnd() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("salinity_start").lte(queryDTO.getSalinityEnd()),
                            Criteria.where("salinity_end").lte(queryDTO.getSalinityEnd())
                    )
            );
        }

        // 添加深度范围查询
        if (queryDTO.getDepthStart() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("depth_start").gte(queryDTO.getDepthStart()),
                            Criteria.where("depth_end").gte(queryDTO.getDepthStart())
                    )
            );
        }
        if (queryDTO.getDepthEnd() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("depth_start").lte(queryDTO.getDepthEnd()),
                            Criteria.where("depth_end").lte(queryDTO.getDepthEnd())
                    )
            );
        }

        // 添加压力范围查询
        if (queryDTO.getPressureStart() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("pressure_start").gte(queryDTO.getPressureStart()),
                            Criteria.where("pressure_end").gte(queryDTO.getPressureStart())
                    )
            );
        }
        if (queryDTO.getPressureEnd() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("pressure_start").lte(queryDTO.getPressureEnd()),
                            Criteria.where("pressure_end").lte(queryDTO.getPressureEnd())
                    )
            );
        }

        // 添加pH范围查询
        if (queryDTO.getPhStart() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("ph_start").gte(queryDTO.getPhStart()),
                            Criteria.where("ph_end").gte(queryDTO.getPhStart())
                    )
            );
        }
        if (queryDTO.getPhEnd() != null) {
            conditionList.add(
                    new Criteria().orOperator(
                            Criteria.where("ph_start").lte(queryDTO.getPhEnd()),
                            Criteria.where("ph_end").lte(queryDTO.getPhEnd())
                    )
            );
        }

        // 添加MASH-Lake模糊查询
        if (StrUtil.isNotBlank(queryDTO.getMashLake())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getMashLake() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("mash_lake").regex(pattern));
        }

        // 添加MASH-Ocean-V2模糊查询
        if (StrUtil.isNotBlank(queryDTO.getMashOceanV2())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getMashOceanV2() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("mash_ocean_v2").regex(pattern));
        }

        // 添加Tara-Ocean模糊查询
        if (StrUtil.isNotBlank(queryDTO.getTaraOcean())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getTaraOcean() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("tara_ocean").regex(pattern));
        }

        // 添加MASH-ChinaSea模糊查询
        if (StrUtil.isNotBlank(queryDTO.getMashChinaSea())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getMashChinaSea() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("mash_china_sea").regex(pattern));
        }

        // 添加MEER模糊查询
        if (StrUtil.isNotBlank(queryDTO.getMeer())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getMeer() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("meer").regex(pattern));
        }

        // 添加元数据相关字段模糊查询
        if (StrUtil.isNotBlank(queryDTO.getSamplingSubstrate())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getSamplingSubstrate() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("sampling_substrate").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getBiome())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getBiome() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("biome").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getCriticalZone())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getCriticalZone() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("critical_zone").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getCountry())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getCountry() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("country").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getHydrosphereType())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getHydrosphereType() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("hydrosphere_type").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getWaterBodyTypeByClassification())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getWaterBodyTypeByClassification() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("water_body_type_by_classification").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getWaterBodyTypeByGeographic())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getWaterBodyTypeByGeographic() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("water_body_type_by_geographic").regex(pattern));
        }

        if (StrUtil.isNotBlank(queryDTO.getWaterBodyName())) {
            Pattern lakePattern = Pattern.compile("^.*" + "lake " + queryDTO.getWaterBodyName() + ".*$", Pattern.CASE_INSENSITIVE);
            Pattern patternLake = Pattern.compile("^.*" + queryDTO.getWaterBodyName() + " lake" + ".*$", Pattern.CASE_INSENSITIVE);
            Pattern riverPattern = Pattern.compile("^.*" + "river " + queryDTO.getWaterBodyName() + ".*$", Pattern.CASE_INSENSITIVE);
            Pattern patternRiver = Pattern.compile("^.*" + queryDTO.getWaterBodyName() + " river" + ".*$", Pattern.CASE_INSENSITIVE);

            conditionList.add(new Criteria().orOperator(
                    Criteria.where("water_body_name").is(queryDTO.getWaterBodyName()),
                    Criteria.where("water_body_name").regex(lakePattern),
                    Criteria.where("water_body_name").regex(patternLake),
                    Criteria.where("water_body_name").regex(riverPattern),
                    Criteria.where("water_body_name").regex(patternRiver))

            );
        }

        if (StrUtil.isNotBlank(queryDTO.getWaterBodyType())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getWaterBodyType() + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where("water_body_type").regex(pattern));
        }

        // todo latitue and longitude
        if (queryDTO.getLatitudeStart() != -90 || queryDTO.getLatitudeEnd() != 90) {
            conditionList.add(Criteria.where("latitude").gte(queryDTO.getLatitudeStart()).lte(queryDTO.getLatitudeEnd()));
        }
        if (queryDTO.getLongitudeStart() != -180 || queryDTO.getLongitudeEnd() != 180) {
            conditionList.add(Criteria.where("longitude").gte(queryDTO.getLongitudeStart()).lte(queryDTO.getLongitudeEnd()));
        }

        // hasResult
        if (StrUtil.isNotBlank(queryDTO.getHasResult())) {
            conditionList.add(Criteria.where("has_result").is(queryDTO.getHasResult()));
        }


        if (CollUtil.isEmpty(conditionList)) {
            return new Criteria();
        }
        return new Criteria().andOperator(conditionList);
    }

    @Override
    public List<String> findFieldDistinct(SamplesQueryDTO samplesQueryDTO) {
        Criteria criteria = getCriteriaFromQueryDTO(samplesQueryDTO);

        return mongoTemplate.findDistinct(new Query(criteria), samplesQueryDTO.getField(), Samples.class, String.class);
    }

    @Override
    public List<String> findDistinctLatLon(SamplesQueryDTO queryDTO) {
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);
        Query query = new Query();
        query.addCriteria(criteria);

        return mongoTemplate.findDistinct(query, "lat_lon", Samples.class, String.class);
    }

    @Override
    public List<Map<String, Object>> findDistinctLatLonWithType(SamplesQueryDTO queryDTO) {
        // 创建查询条件
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);

        // 创建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(
                // 匹配条件
                Aggregation.match(criteria),
                // 分组操作 - 按lat_lon分组，取第一个water_body_type_by_geographic值
                Aggregation.group("lat_lon")
                        .first("hydrosphere_type").as("hydrosphereType")
                        // 将lat_lon拆分为latitude和longitude
                        .first("latitude").as("latitude")
                        .first("longitude").as("longitude")
        );

        // 执行聚合查询
        AggregationResults<Map> results = mongoTemplate.aggregate(
                aggregation, "samples", Map.class
        );

        // 将结果转换为所需的格式
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map map : results.getMappedResults()) {
            if (map.get("_id") != null) {
                Map<String, Object> pointData = new LinkedHashMap<>();
                pointData.put("latitude", map.get("latitude"));
                pointData.put("longitude", map.get("longitude"));
                pointData.put("hydrosphereType", map.get("hydrosphereType"));
                resultList.add(pointData);
            }
        }

        return resultList;
    }

    @Override
    public List<String> findFieldDistinct(String field, String keyword) {
        ArrayList<Criteria> conditionList = new ArrayList<>();
        conditionList.add(Criteria.where(field).exists(true).ne(null));
        if (!StrUtil.isBlank(keyword)) {
            Pattern pattern = Pattern.compile("^.*" + keyword + ".*$", Pattern.CASE_INSENSITIVE);
            conditionList.add(Criteria.where(field).regex(pattern));
        }
        conditionList.add(Criteria.where(field).ne("-"));
        Query query = new Query();
        query.limit(1000);
        query.addCriteria(new Criteria().andOperator(conditionList));
        return mongoTemplate.findDistinct(query, field, Samples.class, String.class);
    }

    public Map<String, Long> getWaterBodyTypeByClassificationDistribution(SamplesQueryDTO queryDTO) {
        // 获取查询条件
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);

        // 创建聚合管道
        Aggregation aggregation = createAggregation(criteria, "water_body_type_by_classification");

        // 执行聚合查询并返回结果
        return executeAggregation(aggregation);
    }

    @Override
    public Map<String, Long> getTechnologyTypeDistribution(SamplesQueryDTO queryDTO) {
        // 获取查询条件
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);

        // 创建聚合管道
        Aggregation aggregation = createAggregation(criteria, "technology_type");

        // 执行聚合查询并返回结果
        return executeAggregation(aggregation);
    }

    @Override
    public Map<String, Long> getWaterBodyTypeByGeographicDistribution(SamplesQueryDTO queryDTO) {
        // 获取查询条件
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);

        // 创建聚合管道
        Aggregation aggregation = createAggregation(criteria, "water_body_type_by_geographic");

        // 执行聚合查询并返回结果
        return executeAggregation(aggregation);
    }


    /**
     * 创建聚合管道
     *
     * @param criteria   查询条件
     * @param groupField 分组字段
     * @return 聚合管道
     */
    private Aggregation createAggregation(Criteria criteria, String groupField) {
        // 创建分组聚合操作，按指定字段分组计数
        GroupOperation groupOperation = Aggregation.group(groupField).count().as("count");
        // 创建排序操作，按count降序排列
        SortOperation sortOperation = Aggregation.sort(Sort.Direction.DESC, "count");

        // 构建聚合管道
        if (criteria != null && !criteria.equals(new Criteria())) {
            return Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    groupOperation,
                    sortOperation
            );
        } else {
            return Aggregation.newAggregation(
                    groupOperation,
                    sortOperation
            );
        }
    }

    /**
     * 执行聚合查询
     *
     * @param aggregation 聚合管道
     * @return 聚合结果Map
     */
    private Map<String, Long> executeAggregation(Aggregation aggregation) {
        // 执行聚合查询
        AggregationResults<CountResult> results = mongoTemplate.aggregate(
                aggregation, "samples", CountResult.class);

        // 将结果转换为Map
        Map<String, Long> resultMap = new LinkedHashMap<>();
        results.getMappedResults().forEach(result -> {
            // 处理空值情况
            String key = result.getId();
            if (StrUtil.isNotBlank(key)) {
                resultMap.put(key, result.getCount());
            }
        });

        return resultMap;
    }

    /**
     * 聚合查询结果类
     */
    private static class CountResult {
        private String id;
        private Long count;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }
    }

    @Override
    public List<Map<String, Object>> getHomeMapLocation(HomeMapQueryDTO queryDTO) {
        ArrayList<Criteria> conditionList = new ArrayList<>();

        // 添加经纬度有效性筛选条件，确保经纬度字段存在且不为空
        conditionList.add(Criteria.where("latitude").exists(true).ne(null));
        conditionList.add(Criteria.where("longitude").exists(true).ne(null));

        // 处理hydrosphereType条件 - 不是All时添加筛选条件
        if (StrUtil.isNotBlank(queryDTO.getHydrosphereType()) && !StrUtil.equalsIgnoreCase(queryDTO.getHydrosphereType(), "All")) {
            conditionList.add(Criteria.where("hydrosphere_type").is(queryDTO.getHydrosphereType()));
        }

        // 处理waterBodyType条件
        if (StrUtil.isNotBlank(queryDTO.getWaterBodyType()) && !StrUtil.equalsIgnoreCase(queryDTO.getWaterBodyType(), "All")) {
            conditionList.add(Criteria.where(queryDTO.getWaterBodyTypeCategory()).is(queryDTO.getWaterBodyType()));
        }

        Criteria criteria = new Criteria().andOperator(conditionList);

        // 创建聚合管道
        List<AggregationOperation> operations = new ArrayList<>();

        // 添加匹配操作
        if (!criteria.equals(new Criteria())) {
            operations.add(Aggregation.match(criteria));
        }

        // 添加用于排序的 isCriticalZoneIn 字段
        AggregationOperation projectOperation = Aggregation.project(
                "latitude", "longitude", "hydrosphere_type", "critical_zone", "data_source",
                "water_body_type_by_classification", "water_body_type_by_geographic", "bio_project_id", "lat_lon"
        ).and(
                ConditionalOperators.when(Criteria.where("critical_zone").is("IN"))
                        .then(1)
                        .otherwise(0)
        ).as("isCriticalZoneIn");
        operations.add(projectOperation);

        // 添加排序操作，确保 critical_zone = IN 的记录排在每组最前
        operations.add(Aggregation.sort(Sort.by(Sort.Order.desc("isCriticalZoneIn"))));

        // 根据displayBy决定是否进行聚合
        if ("Project".equals(queryDTO.getDisplayBy())) {
            // Project模式下按bio_project_id聚合
            operations.add(
                    Aggregation.group("bio_project_id")
                            .first("latitude").as("latitude")
                            .first("longitude").as("longitude")
                            .first("hydrosphere_type").as("hydrosphereType")
                            .first("critical_zone").as("criticalZone")
                            .first("data_source").as("dataSource")
                            .first("water_body_type_by_classification").as("waterBodyTypeByClassification")
                            .first("water_body_type_by_geographic").as("waterBodyTypeByGeographic")
            );
        } else {
            // Run模式下按lat_lon聚合
            operations.add(
                    Aggregation.group("lat_lon")
                            .first("latitude").as("latitude")
                            .first("longitude").as("longitude")
                            .first("hydrosphere_type").as("hydrosphereType")
                            .first("critical_zone").as("criticalZone")
                            .first("data_source").as("dataSource")
                            .first("water_body_type_by_classification").as("waterBodyTypeByClassification")
                            .first("water_body_type_by_geographic").as("waterBodyTypeByGeographic")
            );
        }

        // 执行聚合查询
        Aggregation aggregation = Aggregation.newAggregation(operations)
                .withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, "samples", Map.class);

        // 处理结果，根据legend添加颜色信息
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map result : results.getMappedResults()) {
            Map<String, Object> pointData = new LinkedHashMap<>();

            // 添加基本坐标信息
            pointData.put("latitude", result.get("latitude"));
            pointData.put("longitude", result.get("longitude"));

            // 根据legend字段确定分组信息
            if (StrUtil.isNotBlank(queryDTO.getLegend())) {
                switch (queryDTO.getLegend()) {
                    case "Hydrosphere Type":
                        pointData.put("legendValue", result.get("hydrosphereType"));
                        if (StrUtil.equalsIgnoreCase((String) result.get("criticalZone"), "IN")) {
                            pointData.put("legendValue", "Critical Zone");
                        }
                        break;
                    case "Critical Zone":
                        pointData.put("legendValue", result.get("criticalZone"));
                        break;
                    case "Data Source":
                        pointData.put("legendValue", result.get("dataSource"));
                        break;
                    default:
                        pointData.put("legendValue", "Default");
                        break;
                }
            } else {
                pointData.put("legendValue", "Default");
            }

            resultList.add(pointData);
        }

        return resultList;
    }

    @Override
    public List<String> findFieldDistinctByHydrosphere(String field, String hydrosphereType) {
        Query query = new Query();

        // 只有当hydrosphereType不是"All"时才添加过滤条件
        if (!StrUtil.equalsIgnoreCase(hydrosphereType, "All")) {
            query.addCriteria(Criteria.where("hydrosphere_type").is(hydrosphereType));
        }

        // 添加字段存在和非空的条件
        query.addCriteria(Criteria.where(field).exists(true).ne(null).ne(""));

        // 执行查询，获取唯一字段值列表
        return mongoTemplate.findDistinct(query, field, "samples", String.class)
                .stream()
                .filter(StrUtil::isNotBlank) // 过滤掉空白值
                .sorted() // 排序
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctRunId(SamplesQueryDTO queryDTO) {
        Criteria criteria = getCriteriaFromQueryDTO(queryDTO);
        Query query = new Query();
        query.addCriteria(criteria);
        return mongoTemplate.findDistinct(query, "run_id", Samples.class, String.class);
    }

    @Override
    public List<String> findByWaterBodyTypeByClassificationLimit30(String waterBodyTypeByClassification) {
        Query query = new Query();

        query.addCriteria(Criteria.where("water_body_type_by_classification").is(waterBodyTypeByClassification).and("has_result").is("Yes")).limit(30);

        return mongoTemplate.findDistinct(query, "run_id", Samples.class, String.class);
    }

    @Override
    public List<String> findByWaterBodyTypeByGeographicLimit30(String waterBodyTypeByGeographic) {
        Query query = new Query();

        query.addCriteria(Criteria.where("water_body_type_by_geographic").is(waterBodyTypeByGeographic).and("has_result").is("Yes")).limit(30);

        return mongoTemplate.findDistinct(query, "run_id", Samples.class, String.class);
    }
}
