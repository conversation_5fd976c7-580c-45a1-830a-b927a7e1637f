#!/usr/bin/env Rscript

#' @Description: Convert KO_Sample count to relative abundance and two standardized formats

suppressMessages(library(GetoptLong))
suppressMessages(library(dplyr))

rm(list = ls(all.names = TRUE))

# 自动安装依赖包
packages = c("dplyr")
ipak <- function(pkg){
  new.pkg <- pkg[!(pkg %in% installed.packages()[,"Package"])]
  if(length(new.pkg)) install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
  sapply(pkg, require, character.only = TRUE)
}
ipak(packages)

# 获取参数
GetoptLong(
  "user_Sample_in_file=s", "input KO count table",
  "out_DIR=s", "output directory",
  "prefix=s", "file prefix for outputs",
  "verbose!", "print verbose messages"
)

# 检查输出目录
if(! dir.exists(out_DIR)){
  dir.create(out_DIR, recursive = TRUE)
}

# ========= 读入数据 ==========
Sample_table <- read.csv(user_Sample_in_file, quote = '', sep = '\t',
                         header = TRUE, check.names = FALSE, row.names = 1)

# ========== 记录全0列和行 ==========
zero_cols <- colnames(Sample_table)[colSums(Sample_table) == 0]
zero_rows <- rownames(Sample_table)[rowSums(Sample_table) == 0]

# ========== 1. 相对丰度归一化 ==========
Sample_table_RA <- Sample_table %>%
  {.[rowSums(.) > 0,, drop = FALSE]} %>%
  {.[, colSums(.) > 0, drop = FALSE]} %>%
  {as.data.frame(t(.))} %>%
  {./rowSums(.)} %>%
  {as.data.frame(t(.))} %>%
  {((.) * 1000000)}

# 恢复全0列和行
for (col in zero_cols) {
  Sample_table_RA[, col] <- 0
}
for (row in zero_rows) {
  Sample_table_RA[row, ] <- 0
}

# 重新排序列和行
Sample_table_RA <- Sample_table_RA[rownames(Sample_table), colnames(Sample_table)]

# 保存 RA 文件
write.table(cbind(Orthology_Entry = rownames(Sample_table_RA), Sample_table_RA),
            file = file.path(out_DIR, paste0(prefix, ".KO_Sample.wide.RA.tsv")),
            sep = '\t', quote = FALSE, row.names = FALSE, col.names = TRUE, fileEncoding = "UTF-8")

# ========== 2. log10(x + 1) ==========
Sample_table_log10 <- log10(Sample_table_RA + 1) %>% round(4)
write.table(cbind(Orthology_Entry = rownames(Sample_table_log10), Sample_table_log10),
            file = file.path(out_DIR, paste0(prefix, ".KO_Sample.wide.log10.tsv")),
            sep = '\t', quote = FALSE, row.names = FALSE, col.names = TRUE, fileEncoding = "UTF-8")

# ========== 3. log2(x / rowMean) ==========
row_means <- apply(Sample_table_RA, 1, mean)
Sample_table_avg <- Sample_table_RA
for (col in colnames(Sample_table_RA)) {
  Sample_table_avg[[col]] <- ifelse((Sample_table_RA[[col]] != 0 & row_means != 0),
                                    log2(Sample_table_RA[[col]] / row_means), 0)
}
Sample_table_avg <- round(Sample_table_avg, 4)

write.table(cbind(Orthology_Entry = rownames(Sample_table_avg), Sample_table_avg),
            file = file.path(out_DIR, paste0(prefix, ".KO_Sample.wide.average.tsv")),
            sep = '\t', quote = FALSE, row.names = FALSE, col.names = TRUE, fileEncoding = "UTF-8")

cat("All files generated successfully in: ", out_DIR, "\n")

