# MASH

## 项目介绍

MASH 水圈数据治理脚本

## 安装依赖

```
cd data_process
pip install -r requirements.txt
```

## onnx转换（spring AI使用的格式）

```
python -m venv spring_venv

./spring_venv/Scripts/activate

(venv) pip install --upgrade pip
(venv) pip install accelerate optimum onnx onnxruntime sentence-transformers
(venv) optimum-cli export onnx --model sentence-transformers/all-MiniLM-L6-v2 onnx-output-folder

optimum-cli export onnx --model D:\Temp\mash_data\sentence-transformers\all-MiniLM-L6-v2 D:\Temp\mash_data\sentence-transformers\all-MiniLM-L6-v2-onnx

optimum-cli export onnx --model D:\Temp\mash_data\sentence-transformers\all-MiniLM-L6-v2 --task sentence-similarity D:\Temp\mash_data\sentence-transformers\all-MiniLM-L6-v2-onnx
```