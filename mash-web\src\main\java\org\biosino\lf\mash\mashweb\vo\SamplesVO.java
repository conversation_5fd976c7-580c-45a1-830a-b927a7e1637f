package org.biosino.lf.mash.mashweb.vo;

import lombok.Data;

@Data
public class SamplesVO {

    private String id;

    private String runId;

    private String runStatus;

    private String bioProjectId;

    private String bioProjectName;

    private String bioProjectTitle;

    private String bioProjectDescription;

    private String bioProjectStatus;

    private String bioSampleId;

    private String sampleTitle;

    private String sampleDescription;

    private String bioSampleStatus;

    private String dataSource;

    private String organism;

    private String omicsType;

    private String technologyType;

    private String experimentId;

    private String experimentStatus;

    private String latLon;

    private String latitude;

    private String longitude;

    private String geoLocName;

    private String temperature;

    private String temperatureStart;

    private String temperatureEnd;

    private String salinity;

    private String salinityStart;

    private String salinityEnd;

    private String depth;

    private String depthStart;

    private String depthEnd;

    private String pressure;

    private String pressureStart;

    private String pressureEnd;

    private String ph;

    private String phStart;

    private String phEnd;

    private String biome;

    private String biomeTag;

    private String projectTag;

    private String mashLake;

    private String mashOceanV2;

    private String taraOcean;

    private String mashChinaSea;

    private String meer;

    private String samplingSubstrate;

    private String criticalZone;

    private String country;

    private String hydrosphereType;

    private String waterBodyTypeByClassification;

    private String waterBodyTypeByGeographic;

    private String waterBodyName;

    private String waterBodyType;

    private String hasResult;
}
