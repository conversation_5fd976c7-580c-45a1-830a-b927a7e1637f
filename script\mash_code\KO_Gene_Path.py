

import argparse
from collections import defaultdict

def parse_keg(input_file):
    ko_map = defaultdict(lambda: {"genes": "", "pathways": set()})
    current_pathway_id = None

    with open(input_file, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()

            # Pathway 行
            if line.startswith("C"):
                parts = line.split()
                for part in parts:
                    if part.startswith("[PATH:ko"):
                        current_pathway_id = part[6:-1]  # ko00010
                        break

            # KO 行
            elif line.startswith("D") and current_pathway_id:
                parts = line.split(None, 2)
                if len(parts) >= 3:
                    ko_id = parts[1]
                    genes = parts[2]
                    ko_map[ko_id]["genes"] = genes
                    ko_map[ko_id]["pathways"].add(current_pathway_id)

    return ko_map

def write_output(ko_map, output_file):
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("KO\tGenes\tPathwayIDs\n")
        for ko_id in sorted(ko_map.keys()):
            genes = ko_map[ko_id]["genes"]
            pathways = ";".join(sorted(ko_map[ko_id]["pathways"]))
            f.write(f"{ko_id}\t{genes}\t{pathways}\n")

def main():
    parser = argparse.ArgumentParser(description="Parse KEGG .keg file to KO-Gene-Pathway table.")
    parser.add_argument("input_file", help="Input .keg file path")
    parser.add_argument("output_file", help="Output TSV file path")

    args = parser.parse_args()

    ko_map = parse_keg(args.input_file)
    write_output(ko_map, args.output_file)

    print(f"Mapping table saved to: {args.output_file}")

if __name__ == "__main__":
    main()

