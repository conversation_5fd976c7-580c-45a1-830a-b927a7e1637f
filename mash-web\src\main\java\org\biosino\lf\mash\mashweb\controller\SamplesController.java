package org.biosino.lf.mash.mashweb.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.core.page.TableDataInfo;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.*;
import org.biosino.lf.mash.mashweb.mongo.entity.Run;
import org.biosino.lf.mash.mashweb.mongo.repository.RunRepository;
import org.biosino.lf.mash.mashweb.service.AntNestDataApplyService;
import org.biosino.lf.mash.mashweb.service.ExpertQaService;
import org.biosino.lf.mash.mashweb.service.RemoteNodeService;
import org.biosino.lf.mash.mashweb.service.SamplesService;
import org.biosino.lf.mash.mashweb.vo.SamplesVO;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/samples")
@RequiredArgsConstructor
public class SamplesController {

    private final SamplesService samplesService;

    private final RemoteNodeService remoteNodeService;

    private final AntNestDataApplyService antNestDataApplyService;

    private final ExpertQaService expertQaService;

    private final FileProperties fileProperties;

    private final RunRepository runRepository;

    @RequestMapping("/list")
    public TableDataInfo list(@RequestBody SamplesQueryDTO samplesQueryDTO) {
        Page<SamplesVO> page = samplesService.list(samplesQueryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 查询提示信息
     */
    @RequestMapping("/searchSelectWord")
    public AjaxResult searchSelectWord(@RequestBody SamplesQueryDTO samplesQueryDTO) {
        List<String> result = samplesService.filterTips(samplesQueryDTO);
        return AjaxResult.success(result);
    }

    /**
     * 获取run信息
     */
    @RequestMapping("/getRunInfoByRunId")
    public AjaxResult getRunInfoByRunId(String runId) {
        Run run = runRepository.findFirstByRunId(runId).orElseThrow(() -> new ServiceException("run not exist"));
        return AjaxResult.success(run);
    }

    /**
     * 查询所有不重复的lat_lon位置信息
     *
     * @return 位置信息列表
     */
    @RequestMapping("/getMapLocation")
    public AjaxResult getMapLocation(@RequestBody SamplesQueryDTO samplesQueryDTO) {
        List<Map<String, Object>> locationData = samplesService.findDistinctLatLonWithType(samplesQueryDTO);
        return AjaxResult.success(locationData);
    }

    /**
     * 查询node中得数据
     */
    @RequestMapping("/getNodeDataByExperimentIds")
    public AjaxResult getNodeDataByExperimentIds(@RequestBody NodeDataApplyDTO dto) {
        List<JSONObject> result = remoteNodeService.findByExpNos(dto.getTypeNos());
        return AjaxResult.success(result);
    }

    /**
     * 获取NODE数据的可访问状态
     * 根据当前页显示的数据ID，查询所有NODE数据的可访问状态
     */
    @RequestMapping("/getNodeDataAccessStatus")
    public AjaxResult getNodeDataAccessStatus(@RequestBody List<String> ids) {
        Map<String, Object> result = samplesService.getNodeDataAccessStatus(ids);
        return AjaxResult.success(result);
    }

    /**
     * 申请node的数据
     */
    @RequestMapping("/applyNodeData")
    public AjaxResult applyNodeData(@Validated @RequestBody NodeDataApplyDTO dto) {
        expertQaService.checkCaptcha(dto);
        remoteNodeService.applyNodeData(dto);
        return AjaxResult.success();
    }

    /**
     * 申请Ant Nest数据
     *
     * @param dto 申请数据DTO
     * @return 申请结果
     */
    @RequestMapping("/applyAntNestData")
    public AjaxResult applyAntNestData(@Validated @RequestBody AntNestDataApplyDTO dto) {
        expertQaService.checkCaptcha(dto);
        String applicationId = antNestDataApplyService.submitApplication(dto);
        return AjaxResult.success("Application submitted successfully", applicationId);
    }

    /**
     * 图表1，2：获取样本按技术类型统计数据
     * 返回每种技术类型的样本数量统计
     */
    @RequestMapping("/getChartTechnologyTypeDistribution")
    public AjaxResult getChartTechnologyTypeDistribution(@RequestBody SamplesQueryDTO samplesQueryDTO) {
        Map<String, Long> techStats = samplesService.getTechnologyTypeDistribution(samplesQueryDTO);
        return AjaxResult.success(techStats);
    }

    /**
     * 图表3：获取样本水体类型分类统计数据
     * 返回每种水体类型的样本数量统计
     */
    @RequestMapping("/getChartWaterBodyTypeDistribution")
    public AjaxResult getWaterBodyTypeByClassificationDistribution(@RequestBody SamplesQueryDTO samplesQueryDTO) {
        Map<String, Long> stats = samplesService.getWaterBodyTypeByClassificationDistribution(samplesQueryDTO);
        return AjaxResult.success(stats);
    }

    /**
     * 图表4：获取样本按水体类型地理分类统计数据
     * 返回每个水体名称的样本数量统计
     */
    @RequestMapping("/getWaterBodyNameDistribution")
    public AjaxResult getWaterBodyTypeByGeographicDistribution(@RequestBody SamplesQueryDTO samplesQueryDTO) {
        Map<String, Long> stats = samplesService.getWaterBodyTypeByGeographicDistribution(samplesQueryDTO);
        return AjaxResult.success(stats);
    }

    /**
     * 根据水文圈类型获取匹配的水体类型列表
     *
     * @param hydrosphereType 水文圈类型
     * @return 水体类型列表
     */
    @RequestMapping("/getWaterBodyTypeByHydrosphere")
    public AjaxResult getWaterBodyTypeByHydrosphere(@RequestParam String hydrosphereType, @RequestParam String category) {
        List<String> waterBodyTypes = samplesService.getWaterBodyTypeByHydrosphere(hydrosphereType, category);
        return AjaxResult.success(waterBodyTypes);
    }

    /**
     * 获取AntNest管理员邮箱
     *
     * @return 管理员邮箱
     */
    @RequestMapping("/getAntNestAdminEmail")
    public AjaxResult getAntNestAdminEmail() {
        String adminEmail = antNestDataApplyService.getAdminEmail();
        return AjaxResult.success(adminEmail);
    }

    /**
     * 首页 第二屏 地图点
     */
    @RequestMapping("/getHomeMapLocation")
    public AjaxResult getHomeMapLocation(@RequestBody HomeMapQueryDTO homeMapQueryDTO) {
        List<Map<String, Object>> locationData = samplesService.getHomeMapLocation(homeMapQueryDTO);
        return AjaxResult.success(locationData);
    }


    /**
     * 获取Sample的详细信息
     */
    @RequestMapping("/getByRunId")
    public AjaxResult getByRunId(@RequestParam String runId) {
        return AjaxResult.success(null);
    }

    /**
     * 获取sample KO Pathway Detail
     */
    @RequestMapping("/getKoPathwayDetail")
    public AjaxResult getKoPathwayDetail(@RequestParam String runId) {
        List<KoDepthPathwayDTO> result = samplesService.getKoPathwayDetail(runId);
        return AjaxResult.success(result);
    }

    /**
     * 获取html文件
     */
    @RequestMapping("/existKrona")
    public AjaxResult existKrona(@RequestParam String runId) {
        File file = FileUtil.file(fileProperties.getBaseDataDir(), "kronal", runId + ".krona.html");
        return AjaxResult.success(file.exists());
    }
}
