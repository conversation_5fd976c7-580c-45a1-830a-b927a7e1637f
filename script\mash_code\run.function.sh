#! /bin/bash
scripts_path=$1
inputPath=$2
outputPath=$3
KO_list_exist=$4
KO_file_dir=$5


#Step1 准备用户筛选样本的KO-Sample矩阵
sh ${scripts_path}/Function/step1-M1S2.cat_KO_depth_summary.MASH.sh -l ${inputPath}/function_sample_list.tsv -i  ${KO_file_dir}/AAA_KO_depth  -s  ${scripts_path}/Function -p MASH -o  ${outputPath}/Function_Table

#Step2 相对丰度计算，然后使用log10/average标准化MASH.KO_Sample.wide.RA.tsv
Rscript ${scripts_path}/Function/step2-Function_normalize.R --user_Sample_in_file ${outputPath}/Function_Table/AAA_KO_sum/MASH.KO_Sample.wide.tsv --out_DIR ${outputPath}/Function_Table --prefix MASH.KO_Sample.wide


GROUP_FILE=${inputPath}/Group.txt

# 提取第二列的唯一组数（去除表头）
n_groups=$(awk 'NR > 1 {print $2}' "$GROUP_FILE" | sort | uniq | wc -l)

echo "检测到组数: $n_groups"

# 根据组数执行不同脚本
if [ "$n_groups" -ge 2 ]
 then
   #Step3.1差异分析
    Rscript ${scripts_path}/Function/step3-KO_diff.R --user_Sample_in_file ${outputPath}/Function_Table/MASH.KO_Sample.wide.KO_Sample.wide.log10.tsv --Group_file ${inputPath}/Group.txt --KO_list_exist ${KO_list_exist}  --input  ${inputPath}/  --out_DIR  ${outputPath}/Function_Table --prefix MASH_KO_diff --verbose

    #Step4.1绘制KO丰度热图
    Rscript ${scripts_path}/Function/step4-KO_heatmap.R --log10_file ${outputPath}/Function_Table/MASH.KO_Sample.wide.KO_Sample.wide.log10.tsv --average_file ${outputPath}/Function_Table/MASH.KO_Sample.wide.KO_Sample.wide.average.tsv --Group_file ${inputPath}/Group.txt --diffKO_file ${outputPath}/Function_Table/MASH_KO_diff_top30_diff_results.csv --out_DIR ${outputPath}/Function_Heatmap_Figure --prefix top30_KO --verbose

else
   #Step3.2提取丰度排名前30的KO丰度矩阵以及KO_Genen_Path表
   python3 ${scripts_path}/Function/step3-KO_depth_Group1.py   --abundance ${outputPath}/Function_Table/MASH.KO_Sample.wide.KO_Sample.wide.log10.tsv --input_dir ${inputPath} --out_dir ${outputPath}/Function_Table --KO_list_exist ${KO_list_exist}

   #Step4.2绘制KO丰度热图
   Rscript ${scripts_path}/Function/step4-KO_heatmap_Group1.R --log10_file ${outputPath}/Function_Table/Top_30_KO_Abundance.tsv --abundance_file ${outputPath}/Function_Table/MASH.KO_Sample.wide.KO_Sample.wide.average.tsv --annotation_file ${outputPath}/Function_Table/Top_30_KO_Gene_Path.csv --Group_file ${inputPath}/Group.txt --out_DIR ${outputPath}/Function_Heatmap_Figure --prefix top30_KO --verbose

fi
















