#!/usr/bin/env python3

import pandas as pd
import argparse
import os

def load_KO_list(filepath):
    """加载 KO.list 中的 KO 编号"""
    with open(filepath, 'r') as f:
        return set(line.strip() for line in f if line.strip())

def annotate_type(df, ko_list):
    """添加 Type 列（selected / other）"""
    df['Type'] = df['KO'].apply(lambda x: 'selected' if x in ko_list else 'other')
    return df

def main():
    parser = argparse.ArgumentParser(description="Extract top 30 KOs by abundance and annotate with gene/pathway info.")
    parser.add_argument("--abundance", required=True, help="Input KO abundance file (wide format, log10 values).")
    parser.add_argument("--input_dir", required=True, help="Directory containing KO_Gene_Path.tsv (and optionally KO.list).")
    parser.add_argument("--out_dir", required=True, help="Directory to write output files.")
    parser.add_argument("--KO_list_exist", choices=["Yes", "No"], default="No", help="Whether input_dir/KO.list exists and should be used for KO tagging.")
    args = parser.parse_args()

    annotation_path = os.path.join(args.input_dir, "KO_Gene_Path.tsv")
    ko_list_path = os.path.join(args.input_dir, "KO.list")
    out_abundance = os.path.join(args.out_dir, "Top_30_KO_Abundance.tsv")
    out_top_annotation = os.path.join(args.out_dir, "Top_30_KO_Gene_Path.csv")
    out_all_annotation = os.path.join(args.out_dir, "All_KO_Gene_Path.csv")

    if not os.path.exists(annotation_path):
        print(f"Error: KO_Gene_Path.tsv not found in {args.input_dir}")
        return
    os.makedirs(args.out_dir, exist_ok=True)

    abundance_df = pd.read_csv(args.abundance, sep="\t")
    annotation_df = pd.read_csv(annotation_path, sep="\t")

    abundance_df["mean_abundance"] = abundance_df.iloc[:, 1:].mean(axis=1)

    if args.KO_list_exist == "Yes":
        if not os.path.exists(ko_list_path):
            print(f"Error: KO_list_exist=Yes but KO.list not found at {ko_list_path}")
            return
        ko_list = load_KO_list(ko_list_path)

        # KO.list 与 abundance_df 中 KO 的交集
        ko_list_filtered = [ko for ko in ko_list if ko in set(abundance_df["Orthology_Entry"])]

        if len(ko_list_filtered) == 30:
            top30_kos = ko_list_filtered
        elif len(ko_list_filtered) < 30:
            remaining_needed = 30 - len(ko_list_filtered)
            abundance_sorted_kos = abundance_df.sort_values("mean_abundance", ascending=False)["Orthology_Entry"].tolist()
            supplement_kos = [ko for ko in abundance_sorted_kos if ko not in ko_list_filtered]
            supplement_kos = supplement_kos[:remaining_needed]
            top30_kos = ko_list_filtered + supplement_kos
        else:
            top30_kos = ko_list_filtered[:30]
    else:
        top30_kos = abundance_df.sort_values("mean_abundance", ascending=False).head(30)["Orthology_Entry"].tolist()

    # 取 Top 30 丰度表，保持 KO 顺序一致
    top30_df = abundance_df[abundance_df["Orthology_Entry"].isin(top30_kos)].copy()
    top30_df["KO_order"] = top30_df["Orthology_Entry"].apply(lambda x: top30_kos.index(x))
    top30_df = top30_df.sort_values("KO_order").drop(columns=["KO_order", "mean_abundance"])

    top30_df.to_csv(out_abundance, sep="\t", index=False)

    all_kos = set(abundance_df["Orthology_Entry"])
    all_annotation = annotation_df[annotation_df["KO"].isin(all_kos)].copy()
    top30_annotation = annotation_df[annotation_df["KO"].isin(top30_kos)].copy()

    mean_abundance_map = dict(zip(abundance_df["Orthology_Entry"], abundance_df["mean_abundance"]))
    for df in [top30_annotation, all_annotation]:
        df["mean_abundance"] = df["KO"].map(mean_abundance_map)
        df.sort_values("mean_abundance", ascending=False, inplace=True)

    if args.KO_list_exist == "Yes":
        ko_list_set = set(ko_list)
        top30_annotation = annotate_type(top30_annotation, ko_list_set)
        all_annotation = annotate_type(all_annotation, ko_list_set)
    else:
        top30_annotation["Type"] = "other"
        all_annotation["Type"] = "other"

    top30_annotation.to_csv(out_top_annotation, index=False)
    all_annotation.to_csv(out_all_annotation, index=False)

    print("Done. Output files:")
    print(f" - {out_abundance}")
    print(f" - {out_top_annotation}")
    print(f" - {out_all_annotation}")

if __name__ == "__main__":
    main()

