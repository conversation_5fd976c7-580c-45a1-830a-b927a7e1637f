#! /bin/bash
#path="/data/mash"
#group_column="group"
#scripts_path="/data/mash/script/lefse/scripts"
#images_path="/data/mash/script/lefse/images"

group_column="group"

#input_file=$1
#output_file=$2
scripts_path=$1
Domain=$2
level=$3
metadata=$4
outpath=$5


set -e
#source /opt/miniconda3/etc/profile.d/conda.sh
#conda activate python3.7.4
# 可能需要修改调用脚本lefse_format.py的位置及挂载目录 /bdp-picb/hpcimage/16S_scripts/lefse_format.py
# singularity run -B ${path}:${path} -B ${scripts_path}:/scripts ${images_path}/conda-scipy.sif \
# 根据level选择对应的输入文件名
if [ "$level" = "P" ]; then
    input_file=${outpath}/phylum.percents.xls
elif [ "$level" = "C" ]; then
    input_file=${outpath}/class.percents.xls
elif [ "$level" = "O" ]; then
    input_file=${outpath}/order.percents.xls
elif [ "$level" = "F" ]; then
    input_file=${outpath}/family.percents.xls
elif [ "$level" = "G" ]; then
    input_file=${outpath}/genus.percents.xls
elif [ "$level" = "S" ]; then
    input_file=${outpath}/species.percents.xls
else
    echo "Error: Unsupported level '$level'. Please use one of: P, C, O, F, G, S"
    exit 1
fi




python3 ${scripts_path}/Diversity/lefse_diff_scripts/lefse_format.py \
-i ${input_file} \
-m ${metadata} \
-c ${group_column} \
-o ${outpath}/lefse.${Domain}.${level}.txt
