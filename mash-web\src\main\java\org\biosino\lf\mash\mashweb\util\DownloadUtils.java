package org.biosino.lf.mash.mashweb.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.rmi.ServerException;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;

@Slf4j
public class DownloadUtils {

    public static void download(HttpServletRequest request, HttpServletResponse response, File file) throws IOException {
        download(request, response, file, "");
    }

    public static void download(HttpServletRequest request, HttpServletResponse response, String filePath) throws IOException {
        download(request, response, filePath, "");
    }

    public static void download(HttpServletRequest request, HttpServletResponse response, byte[] bytes) throws IOException {
        download(request, response, bytes, "");
    }

    public static void download(HttpServletRequest request, HttpServletResponse response, String filePath, String displayName) throws IOException {
        File file = new File(filePath);

        if (!FileUtil.exist(file)) {
            throw new ServerException("Download Error! The downloaded file does not exist on the service!");
        }

        if (StringUtils.isBlank(displayName)) {
            displayName = file.getName();
        }

        if (!file.exists() || !file.canRead() || !file.isFile()) {
            try (PrintWriter writer = response.getWriter()) {
                response.setContentType("text/html;charset=utf-8");
                writer.write("The downloaded file does not exist on the service!");
                return;
            }
        }

        String userAgent = request.getHeader("User-Agent");
        boolean ie = (userAgent != null) && (userAgent.toLowerCase().contains("msie"));

        response.reset();
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "must-revalidate, no-transform");
        // 适配ruoyi的request.js的download方法
        response.setHeader("filename", displayName);
        response.setDateHeader("Expires", 0L);

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setContentLength((int) file.length());

        final String displayFilename = URLUtil.encode(displayName);
        if (ie) {
            response.setHeader("Content-Disposition", "attachment;filename=\"" + displayFilename + "\"");
        } else {
            response.setHeader("Content-Disposition", "attachment;filename=" + displayFilename);
        }
        initHeader(response, displayFilename);
        BufferedInputStream is = null;
        OutputStream os = null;
        try {

            os = response.getOutputStream();
            is = new BufferedInputStream(Files.newInputStream(file.toPath()));
            IOUtils.copy(is, os);
        } catch (Exception e) {
            log.error("download error:{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(os);
        }
    }

    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

    public static void download(HttpServletRequest request, HttpServletResponse response, File file, String displayName) throws IOException {
        if (StringUtils.isBlank(displayName)) {
            displayName = file.getName();
        }

        if (!FileUtil.exist(file)) {
            throw new ServerException("Download Error! The downloaded file does not exist on the service!");
        }

        if (!file.exists() || !file.canRead() || !file.isFile()) {
            try (PrintWriter writer = response.getWriter()) {
                response.setContentType("text/html;charset=utf-8");
                writer.write("The file you downloaded does not exist!");
                return;
            }
        }

        String userAgent = request.getHeader("User-Agent");
        boolean ie = (userAgent != null) && (userAgent.toLowerCase().contains("msie"));

        response.reset();
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "must-revalidate, no-transform");
        // 适配ruoyi的request.js的download方法
        response.setDateHeader("Expires", 0L);

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setContentLength((int) file.length());

        final String displayFilename = URLUtil.encode(displayName);
        if (ie) {
            response.setHeader("Content-Disposition", "attachment;filename=\"" + displayFilename + "\"");
        } else {
            response.setHeader("Content-Disposition", "attachment;filename=" + displayFilename);
        }
        initHeader(response, displayFilename);
        BufferedInputStream is = null;
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            is = FileUtil.getInputStream(file);
            IOUtils.copy(is, os);
        } catch (Exception e) {
            log.error("download error:{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(os);
        }
    }

    private static void initHeader(final HttpServletResponse response, final String displayFilename) {
        final String otherHeaderKey = "filename";
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, toCommaDelimitedString(CollUtil.toList(otherHeaderKey)));
        response.setHeader(otherHeaderKey, displayFilename);
    }

    protected static String toCommaDelimitedString(List<String> headerValues) {
        StringJoiner joiner = new StringJoiner(", ");
        for (String val : headerValues) {
            if (val != null) {
                joiner.add(val);
            }
        }
        return joiner.toString();
    }

    public static void download(HttpServletRequest request, HttpServletResponse response, byte[] bytes, String displayName) throws IOException {
        if (ArrayUtils.isEmpty(bytes)) {
            throw new ServerException("Download Error! The downloaded file does not exist on the service!");
        }

        String userAgent = request.getHeader("User-Agent");
        boolean ie = (userAgent != null) && (userAgent.toLowerCase().contains("msie"));

        response.reset();
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "must-revalidate, no-transform");
        response.setDateHeader("Expires", 0L);

        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setContentLength(bytes.length);

        final String displayFilename = URLUtil.encode(displayName);
        if (ie) {
            response.setHeader("Content-Disposition", "attachment;filename=\"" + displayFilename + "\"");
        } else {
            response.setHeader("Content-Disposition", "attachment;filename=" + displayFilename);
        }
        initHeader(response, displayFilename);
        BufferedInputStream is = null;
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            is = new BufferedInputStream(new ByteArrayInputStream(bytes));
            IOUtils.copy(is, os);
        } catch (Exception e) {
            log.error("download error:{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(os);
        }
    }

    public static ResponseEntity<Resource> downLoadWithResource(final File file, final String fileName) {
        return downLoadWithResource(file, fileName, false, null);
    }

    public static ResponseEntity<Resource> downLoadWithResource(final File file, final String fileName, final boolean isInline, final MediaType mediaType) {
        if (file == null || !file.exists() || !file.isFile() || !file.canRead()) {
            return ResponseEntity.badRequest().build();
        }
        try {
            final HttpHeaders headers = new HttpHeaders();
            // 发给客户端的额外header名称
            final String otherHeaderKey = "filename";
            headers.setAccessControlExposeHeaders(Collections.singletonList(otherHeaderKey));
            final String encodeFileName = percentEncode(fileName == null ? file.getName() : fileName);
            headers.add(otherHeaderKey, encodeFileName);
            if (isInline) {
                // 浏览器嵌入显示，例如pdf预览
                headers.setContentDisposition(ContentDisposition.parse(String.format("inline; filename=\"%s\"", encodeFileName)));
            } else {
                headers.setContentDisposition(ContentDisposition.parse(String.format("attachment; filename=\"%s\"", encodeFileName)));
            }
            headers.setContentType(mediaType == null ? MediaType.APPLICATION_OCTET_STREAM : mediaType);

            headers.setPragma("no-cache");
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            final FileSystemResource fileResource = new FileSystemResource(file);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(fileResource.contentLength())
                    .body(fileResource);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    public static void downloadAndDelete(File excelFile, final HttpServletRequest request, final HttpServletResponse response) {
        try {
            if (excelFile != null && excelFile.exists() && excelFile.isFile()) {
                DownloadUtils.download(request, response, excelFile);
            }
        } catch (IOException e) {
            throw new ServiceException("导出出错:" + e.getMessage());
        } finally {
            if (excelFile != null && excelFile.exists() && excelFile.isFile()) {
                excelFile.delete();
            }
        }
    }

}
