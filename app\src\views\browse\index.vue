<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15">
        <el-col :span="24">
          <!-- 主要内容区域 -->
          <div class="card w-100 mb-1">
            <!-- 第二行：地图 -->
            <el-row :gutter="15">
              <el-col :span="5">
                <div
                  id="antNestChart"
                  v-loading="antNestChartLoading"
                  class="chart-item"
                ></div>
                <div
                  id="nodeChart"
                  v-loading="nodeChartLoading"
                  class="chart-item"
                ></div>
              </el-col>
              <el-col :span="14">
                <div class="text-center">
                  <h4 style="margin: 0; color: #1e7cb2">
                    To zoom in/out on the map, simply click on any dot to select
                    it. Scroll down the page to search and browse sample list.
                  </h4>
                </div>
                <div
                  v-if="!showAllData"
                  id="dataMap"
                  v-loading="mapLoading || geoDataLoading"
                  class="map-container"
                ></div>
                <div
                  v-else
                  id="filterMap"
                  v-loading="mapLoading || geoDataLoading"
                  class="map-container"
                ></div>
              </el-col>
              <el-col :span="5">
                <div
                  id="waterBodyTypeByGeographic"
                  v-loading="waterBodyTypeByGeographicLoading"
                  class="chart-item"
                ></div>
                <div
                  id="waterBodyTypeByClassification"
                  v-loading="waterBodyTypeByClassificationLoading"
                  class="chart-item"
                ></div>
              </el-col>
            </el-row>
          </div>

          <!-- 表格区域 -->
          <div class="card">
            <div class="d-flex justify-space-between">
              <h3 class="mt-0">Sample List</h3>
              <div>
                <el-tooltip
                  content="Please select the data you want to export"
                  placement="bottom"
                >
                  <el-button
                    :disabled="selectRows.length === 0"
                    type="primary"
                    class="mr-1"
                    @click="exportData"
                    >Export Data
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="Please select NODE data to apply"
                  placement="bottom"
                >
                  <el-button
                    type="warning"
                    class="mr-1"
                    :disabled="selectedNodeData.length === 0"
                    @click="showNodeDialog"
                    >Apply NODE Data
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  content="Please select at least one data to apply"
                  placement="bottom"
                >
                  <el-button
                    type="warning"
                    class="mr-1"
                    :disabled="selectedAntNestData.length === 0"
                    @click="() => (antNestDialogVisible = true)"
                    >Apply Ant Nest Data
                  </el-button>
                </el-tooltip>
                <el-button
                  type="success"
                  :disabled="!hasValidSelection"
                  @click="visible = true"
                >
                  Add analysis data to cart
                </el-button>
              </div>
            </div>

            <el-divider class="mt-1"></el-divider>

            <!-- 区域选择复选框 -->
            <el-row :gutter="15" class="region-control-row">
              <el-col :span="20">
                <div class="region-selector">
                  <el-checkbox v-model="selectedRegions.baseInfo" disabled
                    >Archive Information
                  </el-checkbox>
                  <el-checkbox v-model="selectedRegions.environment"
                    >Environment Information
                  </el-checkbox>
                  <el-checkbox v-model="selectedRegions.metadata"
                    >Other Metadata
                  </el-checkbox>
                  <el-checkbox v-model="selectedRegions.datasets"
                    >Characterized Datasets
                  </el-checkbox>
                  <div class="table-tip">
                    <el-icon>
                      <InfoFilled />
                    </el-icon>
                    <span>Click to show columns</span>
                  </div>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="button-group">
                  <el-button
                    type="primary"
                    icon="Search"
                    @click="getDataList(false)"
                    >Search
                  </el-button>
                  <el-button icon="RefreshRight" @click="resetQuery"
                    >Reset
                  </el-button>
                </div>
              </el-col>
            </el-row>

            <el-table
              v-loading="loading"
              height="500"
              tooltip-effect="dark"
              :data="tableData"
              :header-cell-style="getHeaderStyle"
              row-key="id"
              border
              :stripe="true"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                :reserve-selection="true"
                type="selection"
                width="55"
                fixed="left"
              />

              <!-- Omics Data Archive Information -->
              <el-table-column
                label="Omics Data Archive Information"
                align="center"
              >
                <el-table-column
                  label="Run ID"
                  prop="runId"
                  width="150"
                  fixed="left"
                >
                  <template #header>
                    <div class="table-header">
                      Run ID
                      <el-autocomplete
                        v-model="queryParams.runId"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'runId', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :popper-append-to-body="false"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                  <template #default="scope">
                    <a
                      v-if="scope.row.dataSource === 'AntNest'"
                      :href="`https://www.ncbi.nlm.nih.gov/sra/${scope.row.runId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.runId }}</a
                    >
                    <a
                      v-else-if="
                        scope.row.dataSource === 'NODE' &&
                        scope.row.runStatus === 'Accessible'
                      "
                      :href="`https://www.biosino.org/node/run/detail/${scope.row.runId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.runId }}</a
                    >
                    <span v-else
                      >{{ scope.row.runId }}
                      <el-tooltip
                        content="The current access status of this dataset is Conditional Access. To obtain its Sample Collection Environment Information (provided by the data owner), users need to apply to the owner via the NODE database."
                      >
                        <el-icon v-show="requestCompleted" color="#ef4444">
                          <Lock />
                        </el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="BioProject ID"
                  prop="bioProjectId"
                  width="150"
                >
                  <template #header>
                    <div class="table-header">BioProject ID</div>
                    <el-autocomplete
                      v-model="queryParams.bioProjectId"
                      size="small"
                      :fetch-suggestions="
                        (queryString, cb) => {
                          remoteSelect(queryString, 'bioProjectId', cb);
                        }
                      "
                      placeholder="Filter"
                      :teleported="true"
                      :loading="selectLoading"
                      clearable
                    >
                    </el-autocomplete>
                  </template>
                  <template #default="scope">
                    <a
                      v-if="scope.row.dataSource === 'AntNest'"
                      :href="`https://www.ncbi.nlm.nih.gov/bioproject/?term=${scope.row.bioProjectId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.bioProjectId }}</a
                    >
                    <a
                      v-else-if="
                        scope.row.dataSource === 'NODE' &&
                        scope.row.runStatus === 'Accessible'
                      "
                      :href="`https://www.biosino.org/node/project/detail/${scope.row.bioProjectId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.bioProjectId }}</a
                    >
                    <span v-else>{{ scope.row.bioProjectId }} </span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="BioSample ID"
                  prop="bioSampleId"
                  width="150"
                >
                  <template #header>
                    <div class="table-header">
                      BioSample ID
                      <el-autocomplete
                        v-model="queryParams.bioSampleId"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'bioSampleId', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                  <template #default="scope">
                    <a
                      v-if="scope.row.dataSource === 'AntNest'"
                      :href="`https://www.ncbi.nlm.nih.gov/biosample/?term=${scope.row.bioSampleId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.bioSampleId }}</a
                    >
                    <a
                      v-else-if="
                        scope.row.dataSource === 'NODE' &&
                        scope.row.runStatus === 'Accessible'
                      "
                      :href="`https://www.biosino.org/node/sample/detail/${scope.row.bioSampleId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.bioSampleId }}</a
                    >
                    <span v-else>{{ scope.row.bioSampleId }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="Experiment ID"
                  prop="experimentId"
                  width="150"
                >
                  <template #header>
                    <div class="table-header">
                      Experiment ID
                      <el-autocomplete
                        v-model="queryParams.experimentId"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'experimentId', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                  <template #default="scope">
                    <a
                      v-if="scope.row.dataSource === 'AntNest'"
                      :href="`https://www.ncbi.nlm.nih.gov/sra/${scope.row.experimentId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.experimentId }}</a
                    >
                    <a
                      v-else-if="
                        scope.row.dataSource === 'NODE' &&
                        scope.row.runStatus === 'Accessible'
                      "
                      :href="`https://www.biosino.org/node/experiment/detail/${scope.row.experimentId}`"
                      target="_blank"
                      class="text-primary"
                      >{{ scope.row.experimentId }}</a
                    >
                    <span v-else>{{ scope.row.experimentId }} </span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="Omics Type"
                  prop="omicsType"
                  width="140"
                >
                  <template #header>
                    <div class="table-header">
                      Omics Type
                      <el-autocomplete
                        v-model="queryParams.omicsType"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'omicsType', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="Technology Type"
                  prop="technologyType"
                  width="140"
                >
                  <template #header>
                    <div class="table-header">
                      Technology Type
                      <el-autocomplete
                        v-model="queryParams.technologyType"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'technologyType', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="Data Source"
                  prop="dataSource"
                  width="140"
                >
                  <template #header>
                    <div class="table-header">
                      Data Source
                      <el-select
                        v-model="queryParams.dataSource"
                        :teleported="false"
                        size="small"
                        placeholder="Filter"
                        clearable
                      >
                        <el-option label="AntNest" value="AntNest"></el-option>
                        <el-option label="NODE" value="NODE"></el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="Organism" prop="organism" width="180">
                  <template #header>
                    <div class="table-header">
                      Organism
                      <el-autocomplete
                        v-model="queryParams.organism"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'organism', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="Result Exist"
                  prop="hasResult"
                  width="200"
                  fixed="left"
                >
                  <template #header>
                    <div class="table-header">
                      Analysis Result Exist
                      <el-autocomplete
                        v-model="queryParams.hasResult"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, 'hasResult', cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :popper-append-to-body="false"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                  <template #default="scope">
                    <router-link
                      v-if="scope.row.hasResult === 'Yes'"
                      :to="`/sample/detail/${scope.row.runId}`"
                    >
                      <span class="text-primary">{{
                        scope.row.hasResult
                      }}</span>
                    </router-link>
                    <span v-else>{{ scope.row.hasResult }}</span>
                  </template>
                </el-table-column>
              </el-table-column>

              <!-- Sample Collection Environment Information -->
              <el-table-column
                v-if="selectedRegions.environment"
                label="Sample Collection Environment Information (Provided by Data Owner)"
                align="center"
              >
                <el-table-column label="Lat Lon" prop="latitude" width="175">
                  <template #header>
                    <div class="table-header">
                      <span>Latitude</span>
                      <div class="filter-row">
                        <div class="filter-controls">
                          <el-input
                            v-model="queryParams.latitudeStart"
                            type="number"
                            size="small"
                            max="90"
                            min="-90"
                            style="width: 70px"
                          />
                          ~
                          <el-input
                            v-model="queryParams.latitudeEnd"
                            type="number"
                            size="small"
                            max="90"
                            min="-90"
                            style="width: 70px"
                          />
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="Lat Lon" prop="longitude" width="175">
                  <template #header>
                    <div class="table-header">
                      <span>Longitude</span>
                      <div class="filter-row">
                        <div class="filter-controls">
                          <el-input
                            v-model="queryParams.longitudeStart"
                            type="number"
                            size="small"
                            style="width: 70px"
                            max="180"
                            min="-180"
                          />
                          ~
                          <el-input
                            v-model="queryParams.longitudeEnd"
                            type="number"
                            size="small"
                            style="width: 70px"
                            max="180"
                            min="-180"
                          />
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-for="(col, index) in environmentColumns.slice(2)"
                  :key="index"
                  :label="col.label"
                  :prop="col.prop"
                  :width="col.width"
                >
                  <template #header>
                    <div class="table-header">
                      {{ col.label }}
                      <template
                        v-if="
                          [
                            'temperature',
                            'salinity',
                            'depth',
                            'pressure',
                            'ph',
                          ].includes(col.prop)
                        "
                      >
                        <div class="filter-row">
                          <div class="filter-controls">
                            <el-input
                              v-model="queryParams[`${col.prop}Start`]"
                              type="number"
                              size="small"
                              style="width: 70px"
                              placeholder="Min"
                            />
                            ~
                            <el-input
                              v-model="queryParams[`${col.prop}End`]"
                              type="number"
                              size="small"
                              style="width: 70px"
                              placeholder="Max"
                            />
                          </div>
                        </div>
                      </template>
                      <el-autocomplete
                        v-else
                        v-model="queryParams[col.prop]"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, `${col.prop}`, cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                </el-table-column>
              </el-table-column>
              <!-- Other Sample Metadata -->
              <el-table-column
                v-if="selectedRegions.metadata"
                label="Other Sample Metadata (Derived from AI Model Governance)"
                align="center"
              >
                <el-table-column
                  v-for="(col, index) in metadataColumns"
                  :key="index"
                  :label="col.label"
                  :prop="col.prop"
                  :width="col.width"
                >
                  <template #header>
                    <div class="table-header">
                      {{ col.label }}
                      <el-autocomplete
                        v-model="queryParams[col.prop]"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, `${col.prop}`, cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                </el-table-column>
              </el-table-column>
              <!-- Inclusion in Characterized Datasets/Studies -->
              <el-table-column
                v-if="selectedRegions.datasets"
                label="Inclusion in Characterized Datasets/Studies"
                align="center"
              >
                <el-table-column
                  v-for="(col, index) in datasetsColumns"
                  :key="index"
                  :label="col.label"
                  :prop="col.prop"
                  :width="col.width"
                >
                  <template #header>
                    <div class="table-header">
                      {{ col.label }}
                      <el-select
                        v-if="col.option"
                        v-model="queryParams[col.prop]"
                        :teleported="false"
                        size="small"
                        placeholder="Filter"
                        clearable
                      >
                        <el-option
                          v-for="item in col.option"
                          :key="item"
                          :label="item"
                          :value="item"
                        />
                      </el-select>
                      <el-autocomplete
                        v-else
                        v-model="queryParams[col.prop]"
                        size="small"
                        :fetch-suggestions="
                          (queryString, cb) => {
                            remoteSelect(queryString, `${col.prop}`, cb);
                          }
                        "
                        placeholder="Filter"
                        :teleported="true"
                        :loading="selectLoading"
                        clearable
                      >
                      </el-autocomplete>
                    </div>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table>

            <pagination
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              :total="total"
              class="mb-1"
              :auto-scroll="false"
              @pagination="getDataList(true)"
            />
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- NODE Data Application Dialog -->
    <el-dialog
      v-model="nodeDialogVisible"
      title="Apply NODE Data"
      width="700px"
    >
      <div class="notice-box">
        <el-icon class="notice-icon">
          <InfoFilled />
        </el-icon>
        <div class="notice-content">
          <h4>Important Notice</h4>
          <p>
            For UnAccessible data, you need to check the checkbox and enter your
            <a
              href="https://www.biosino.org/node/"
              target="_blank"
              class="text-primary"
              >NODE</a
            >
            account credentials to apply.
          </p>
        </div>
      </div>

      <el-table
        ref="nodeTableRef"
        :data="nodeData"
        style="width: 100%"
        border
        class="node-table hide-select-all"
        @selection-change="handleNodeSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="row => row.visibleStatus !== 'Accessible'"
        >
        </el-table-column>
        <el-table-column prop="expNo" label="NODE Experiment ID" />
        <el-table-column prop="visibleStatus" label="Status" width="140">
          <template #default="scope">
            <div
              class="status-cell"
              :class="scope.row.visibleStatus.toLowerCase()"
            >
              <el-icon v-if="scope.row.visibleStatus === 'Accessible'">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else>
                <Lock />
              </el-icon>
              <span>{{ scope.row.visibleStatus }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="hasSelectedUnAccessible" class="login-section">
        <el-form
          ref="nodeFormRef"
          :model="nodeCredentials"
          :rules="nodeFormRules"
          label-width="180px"
          class="node-form"
        >
          <el-form-item label="NODE Username" prop="username">
            <el-input
              v-model="nodeCredentials.username"
              placeholder="Enter your NODE username"
              class="login-input"
            >
              <template #prefix>
                <el-icon>
                  <User />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="NODE Password" prop="password">
            <el-input
              v-model="nodeCredentials.password"
              type="password"
              placeholder="Enter your NODE password"
              class="login-input"
              show-password
            >
              <template #prefix>
                <el-icon>
                  <Lock />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="Request Text">
            <el-input
              v-model="nodeCredentials.description"
              type="textarea"
              :rows="4"
              placeholder="Enter your request instructions"
              class="login-input"
            >
            </el-input>
          </el-form-item>
          <!-- 验证码放在最后 -->
          <el-form-item label="Verification code" prop="captchaCode">
            <div class="captcha-container">
              <el-input
                v-model="nodeCredentials.captchaCode"
                size="large"
                auto-complete="off"
                placeholder="Verification code"
                style="width: 200px"
              >
              </el-input>
              <div class="login-code">
                <img
                  :src="nodeCodeUrl"
                  class="login-code-img"
                  alt=""
                  @click="getNodeCode"
                />
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="nodeDialogVisible = false">Cancel</el-button>
          <el-button
            type="primary"
            :disabled="!canSubmit"
            :loading="nodeSubmitLoading"
            @click="submitNodeApplication"
          >
            Submit
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Ant Nest Data Application Dialog -->
    <el-dialog
      v-model="antNestDialogVisible"
      title="Apply Ant Nest Data"
      width="700px"
    >
      <div class="notice-box">
        <el-icon class="notice-icon">
          <InfoFilled />
        </el-icon>
        <div class="notice-content">
          <h4>Important Notice</h4>
          <p>
            Please fill in the following information to apply for Ant Nest data
            access.
          </p>
          <p>
            If you have more questions, you can contact AntNest administrator,
            email:
            <a class="text-primary" :href="`mailto:${antNestAdminEmail}`">{{
              antNestAdminEmail
            }}</a>
          </p>
        </div>
      </div>

      <!-- Selected Run IDs -->
      <div>
        <h4>Selected Runs:</h4>
        <div style="max-height: 100px; overflow-y: auto">
          <el-tag
            v-for="item in selectedAntNestData"
            :key="item.runId"
            type="info"
            effect="plain"
            size="small"
            style="margin: 3px"
          >
            {{ item.runId }}
          </el-tag>
        </div>
      </div>

      <el-form
        ref="antNestFormRef"
        :model="antNestForm"
        :rules="antNestFormRules"
        label-width="120px"
        class="ant-nest-form"
      >
        <el-form-item label="Title" prop="title">
          <el-input
            v-model="antNestForm.title"
            placeholder="Enter the title of your research"
            class="form-input"
          >
            <template #prefix>
              <el-icon>
                <Document />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="Name" prop="name">
          <el-input
            v-model="antNestForm.name"
            placeholder="Enter your full name"
            class="form-input"
          >
            <template #prefix>
              <el-icon>
                <User />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="Institution" prop="institution">
          <el-input
            v-model="antNestForm.institution"
            placeholder="Enter your institution or organization"
            class="form-input"
          >
            <template #prefix>
              <el-icon>
                <OfficeBuilding />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="Email" prop="email">
          <el-input
            v-model="antNestForm.email"
            placeholder="Enter your email address"
            class="form-input"
            type="email"
          >
            <template #prefix>
              <el-icon>
                <Message />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="Request Text" prop="requestText">
          <el-input
            v-model="antNestForm.requestText"
            type="textarea"
            :rows="4"
            placeholder="Enter your request instructions"
            class="form-input"
          >
          </el-input>
        </el-form-item>
        <!-- 验证码放在最后 -->
        <el-form-item label="Verification code" prop="captchaCode">
          <div class="captcha-container">
            <el-input
              v-model="antNestForm.captchaCode"
              size="large"
              auto-complete="off"
              placeholder="Verification code"
              style="width: 200px"
            >
            </el-input>
            <div class="login-code">
              <img
                :src="antNestCodeUrl"
                class="login-code-img"
                alt=""
                @click="getAntNestCode"
              />
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="antNestDialogVisible = false">Cancel</el-button>
          <el-button
            type="primary"
            :disabled="!canSubmitAntNest"
            :loading="antNestSubmitLoading"
            @click="submitAntNestApplication"
          >
            Submit
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <BrowseCart />

  <!--   添加到购物车弹框 -->
  <el-dialog
    v-model="visible"
    title="Add analysis data to cart"
    width="500px"
    :append-to-body="true"
  >
    <div class="add-to-cart-content">
      <!-- 分组选择 -->
      <div class="group-selection">
        <el-radio-group v-model="selectedOption" class="group-options">
          <!-- 创建新分组 -->
          <div class="option-item">
            <el-radio value="new">Create New Group</el-radio>
            <div v-if="selectedOption === 'new'" class="new-group-input">
              <el-input
                v-model="newGroupName"
                placeholder="Enter group name (e.g., GroupA)"
                maxlength="50"
                show-word-limit
              />
            </div>
          </div>

          <!-- 选择现有分组 -->
          <div class="option-item">
            <el-radio value="existing">Add to Existing Group</el-radio>
            <div v-if="selectedOption === 'existing'" class="existing-groups">
              <el-select
                v-model="selectedGroupName"
                :teleported="false"
                placeholder="Select a group"
                style="width: 100%"
              >
                <el-option
                  v-for="group in existingGroups"
                  :key="group"
                  :label="group"
                  :value="group"
                />
              </el-select>
            </div>
          </div>
        </el-radio-group>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer mt-0">
        <el-button @click="visible = false">Cancel</el-button>
        <el-button type="primary" @click="confirmAddToCart">
          Confirm
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onBeforeUnmount,
    onMounted,
    reactive,
    ref,
    toRaw,
    toRefs,
    watch,
  } from 'vue';
  import { useRoute } from 'vue-router';
  import * as echarts from 'echarts';
  import * as XLSX from 'xlsx'; // 导入 xlsx 库
  import {
    Document,
    InfoFilled,
    Lock,
    Message,
    OfficeBuilding,
    User,
  } from '@element-plus/icons-vue';
  import {
    applyAntNestData,
    applyNodeData,
    getAntNestAdminEmail,
    getChartTechnologyTypeDistribution,
    getChartWaterBodyTypeDistribution,
    getMapLocation,
    getNodeDataByExperimentIds,
    getWaterBodyNameDistribution,
    list,
    searchSelectWord,
  } from '@/api/samples';
  import { debounce, formatNumber, trimStr } from '@/utils';
  import { getCaptchaImg } from '@/api/captcha';
  import { doAddVisitsLog } from '@/api/visits_log';
  import { VISITS_LOG_FUNC } from '@/constants';
  import axios from 'axios';
  // import UnifiedCart from '@/components/ShoppingCart/UnifiedCart.vue';
  import BrowseCart from '@/components/ShoppingCart/BrowseCart.vue';
  import { useCartStore } from '@/store/modules/cart';

  const canvasRenderer = L.canvas({ padding: 0.5 });
  let mapLoading = ref(false);
  const { proxy } = getCurrentInstance();
  const route = useRoute();

  // 购物车相关
  const cartStore = useCartStore();
  const visible = ref(false);
  const selectedOption = ref('new');
  const newGroupName = ref('');
  const selectedGroupName = ref('');

  // 从store获取现有分组
  const existingGroups = computed(() => cartStore.getGroupNames.value);

  // 添加地理数据的ref
  const oceanData = ref(null);
  const lakesData = ref(null);
  const riversData = ref(null);
  const geoDataLoading = ref(true);

  // 异步获取地理数据的函数
  const fetchGeoData = async () => {
    try {
      geoDataLoading.value = true;
      // 获取基础路径
      const basePath = import.meta.env.VITE_APP_PUBLIC_PATH || '/';
      const [oceanResponse, lakesResponse, riversResponse] = await Promise.all([
        axios.get(`${basePath}/geojson/ocean.json`),
        axios.get(`${basePath}/geojson/sample_lakes.json`),
        axios.get(`${basePath}/geojson/sample_rivers.json`),
      ]);

      oceanData.value = oceanResponse.data;
      lakesData.value = lakesResponse.data;
      riversData.value = riversResponse.data;
      return true;
    } catch (error) {
      console.error('加载地理数据失败:', error);
      return false;
    } finally {
      geoDataLoading.value = false;
    }
  };

  let allIds = proxy.$route.query.ids;
  let field = proxy.$route.query.field;
  let keyword = proxy.$route.query.keyword;
  let dataset = proxy.$route.query.dataset;
  let longitude = proxy.$route.query.longitude;
  let latitude = proxy.$route.query.latitude;

  // 图表实例的引用
  const charts = reactive({
    antNestChart: null,
    nodeChart: null,
    waterBodyTypeByGeographic: null,
    waterBodyTypeByClassification: null,
  });

  // 区域选择状态
  const selectedRegions = reactive({
    baseInfo: true,
    environment: true,
    datasets: true,
    metadata: true,
  });

  // 表格列定义
  const environmentColumns = [
    { label: 'Latitude', prop: 'latitude', width: '220' },
    { label: 'Longitude', prop: 'longitude', width: '220' },
    { label: 'Geo loc name', prop: 'geoLocName', width: '320' },
    { label: 'Temperature', prop: 'temperature', width: '175' },
    { label: 'Salinity', prop: 'salinity', width: '175' },
    { label: 'Depth', prop: 'depth', width: '175' },
    { label: 'Pressure', prop: 'pressure', width: '175' },
    { label: 'pH', prop: 'ph', width: '175' },
  ];

  const datasetsColumns = [
    {
      label: 'MASH-Lake',
      prop: 'mashLake',
      width: '100',
      option: ['IN', 'OUT'],
    },
    {
      label: 'Mash-Ocean-V2',
      prop: 'mashOceanV2',
      width: '140',
      option: ['IN', 'OUT'],
    },
    {
      label: 'Tara-Ocean',
      prop: 'taraOcean',
      width: '100',
      option: ['IN', 'OUT'],
    },
    {
      label: 'MASH-ChinaSea',
      prop: 'mashChinaSea',
      width: 140,
      option: ['IN', 'OUT'],
    },
    { label: 'MEER', prop: 'meer', width: '80', option: ['IN', 'OUT'] },
  ];

  const waterBodyTypeCategories = {
    'unspecified': {
      items: ['unspecified'],
      color: '#D1D1D1',
    },
    'Inland Water': {
      items: [
        'Lakes',
        'Inland Seas',
        'Wetlands',
        'Rivers',
        'Groundwater',
        'Artificial Water Bodies',
        'Saline lakes',
        'Ice Caps & Glaciers',
        'Soil moisture',
      ],
      color: '#E69100',
    },
    'Marine': {
      items: [
        'Sea',
        'Bay',
        'Gulf',
        'Fjord',
        'Mangrove',
        'Channel',
        'Strait',
        'Lagoon',
        'Marsh & Estuary',
        'Oceans',
      ],
      color: '#F2D643',
    },
  };

  const geographicCategories = {
    'unspecified': {
      items: ['unspecified'],
      color: '#D1D1D1',
    },
    'Inland Water': {
      items: [
        'Asia',
        'North America',
        'Europe',
        'Australia',
        'Oceania',
        'Africa',
        'Antarctica',
        'South America',
      ],
      color: '#E69100',
    },
    'Marine': {
      items: [
        'North Pacific',
        'South Pacific',
        'North Atlantic',
        'Indian Ocean',
        'Arctic Ocean',
        'South Atlantic',
        'Southern Ocean',
        'Mediterranean Sea',
        'Baltic Sea',
      ],
      color: '#F2D643',
    },
  };

  const metadataColumns = [
    { label: 'Sampling Substrate', prop: 'samplingSubstrate', width: '180' },
    { label: 'Biome', prop: 'biome', width: '320' },
    { label: 'Critical Zone', prop: 'criticalZone', width: '120' },
    { label: 'Country', prop: 'country', width: '140' },
    { label: 'Hydrosphere Type', prop: 'hydrosphereType', width: '140' },
    {
      label: 'Water Body Type',
      prop: 'waterBodyTypeByClassification',
      width: '140',
    },
    {
      label: 'Geolocation',
      prop: 'waterBodyTypeByGeographic',
      width: '140',
    },
    { label: 'Water Body Name', prop: 'waterBodyName', width: '140' },
  ];

  function renderMapPoint() {
    // 检查地图是否已初始化
    if (!map.value) {
      console.warn('地图尚未初始化，无法渲染点位');
      return;
    }

    // 检查地理数据是否已加载
    if (!oceanData.value || !lakesData.value || !riversData.value) {
      console.warn('地理数据尚未加载完成，无法渲染点位');
      return;
    }

    // 先清除所有现有的点位图层
    map.value.eachLayer(layer => {
      // 只移除点位图层，保留基础地图图层
      if (layer instanceof L.LayerGroup && !(layer instanceof L.GeoJSON)) {
        map.value.removeLayer(layer);
      }
    });

    mapLoading.value = true;
    getMapLocation(queryParams.value)
      .then(response => {
        let data = response.data;
        var pointsLayer = L.layerGroup(); // 先创建图层组，但不立即添加到地图

        let markers = []; // 用于保存所有 marker

        for (let i = 0; i < data.length; i++) {
          let item = data[i];

          // 根据waterBodyTypeByGeographic字段确定颜色
          let color = '#D1D1D1'; // 默认颜色为unspecified的灰色

          if (item.hydrosphereType === 'Inland water') {
            color = '#E69100';
          } else if (item.hydrosphereType === 'Marine') {
            color = '#F2D643';
          }

          let circleMarker = L.circleMarker([item.latitude, item.longitude], {
            radius: 3,
            fillColor: color,
            color: color,
            opacity: 1,
            weight: 0.5,
            fillOpacity: 1,
            pane: 'pointsPane',
            renderer: canvasRenderer,
          });

          // 添加点击事件
          circleMarker.on('click', function () {
            // 将点击的点的经纬度设置到筛选条件中
            queryParams.value.latitudeStart = item.latitude;
            queryParams.value.latitudeEnd = item.latitude;
            queryParams.value.longitudeStart = item.longitude;
            queryParams.value.longitudeEnd = item.longitude;
            getDataList(); // 点击后立即查询数据
          });

          markers.push(circleMarker); // 先放到数组中
        }

        pointsLayer.addLayer(L.featureGroup(markers)); // 一次性添加所有 marker
        pointsLayer.addTo(map.value); // 最后统一添加到地图上
      })
      .finally(() => {
        mapLoading.value = false;
      });
  }

  onMounted(async () => {
    // 获取AntNest管理员邮箱
    getAntNestAdminEmail().then(res => {
      antNestAdminEmail.value = res.msg;
    });
    // 清理现有图表实例
    Object.values(charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });

    // 首先异步加载地理数据
    await fetchGeoData();

    // 使用nextTick确保DOM完全渲染后再进行图表和地图的初始化
    nextTick(() => {
      // 初始化新的图表实例
      charts.antNestChart = echarts.init(
        document.getElementById('antNestChart'),
      );
      charts.nodeChart = echarts.init(document.getElementById('nodeChart'));
      charts.waterBodyTypeByClassification = echarts.init(
        document.getElementById('waterBodyTypeByClassification'),
      );
      charts.waterBodyTypeByGeographic = echarts.init(
        document.getElementById('waterBodyTypeByGeographic'),
      );

      // 地图初始化
      initMap();
      // 确保地图尺寸正确
      setTimeout(() => {
        if (map.value) {
          map.value.invalidateSize();
        }
      }, 100);

      getDataList();

      // 添加窗口大小变化事件监听
      window.addEventListener('resize', handleResize);
    });
  });

  // 组件卸载前清理资源
  onBeforeUnmount(() => {
    // 清理图表实例
    Object.values(charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });

    // 移除事件监听
    window.removeEventListener('resize', handleResize);
  });

  const sliderLatitude = ref([-90, 90]);
  const sliderLongitude = ref([-180.0, 180.0]);
  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      allIds: allIds ? allIds.split(',') : [],
      field: field || '',
      keyword: keyword || '',
      runId: '',
      bioProjectId: '',
      bioSampleId: '',
      experimentId: '',
      omicsType: '',
      hasResult: '',
      technologyType: '',
      latitudeStart: latitude || sliderLatitude.value[0],
      latitudeEnd: latitude || sliderLatitude.value[1],
      longitudeStart: longitude || sliderLongitude.value[0],
      longitudeEnd: longitude || sliderLongitude.value[1],
      geoLocName: '',
      temperature: '',
      temperatureStart: '',
      temperatureEnd: '',
      salinity: '',
      salinityStart: '',
      salinityEnd: '',
      depth: '',
      depthStart: '',
      depthEnd: '',
      pressure: '',
      pressureStart: '',
      pressureEnd: '',
      ph: '',
      phStart: '',
      phEnd: '',
      mashLake: '',
      mashOceanV2: '',
      taraOcean: '',
      mashChinaSea: '',
      meer: '',
      samplingSubstrate: '',
      biome: '',
      criticalZone: '',
      country: '',
      hydrosphereType: '',
      waterBodyTypeByClassification: '',
      waterBodyTypeByGeographic: '',
      waterBodyName: '',
      waterBodyType: '',
      pageNum: 1,
      pageSize: 20,
    },
    roleList: [],
    loading: false,
    defaultSort: { prop: 'createTime', order: 'descending' },
  });

  const { tableData, total, queryParams, loading, defaultSort } = toRefs(data);

  /** 查询列表数据*/
  function getDataList(pageChange = false) {
    loading.value = true;
    list(queryParams.value)
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
        if (!pageChange) {
          // 重新加载图表数据
          loadChartData();
          // 更新地图点位
          renderMapPoint();
        }

        // 表格数据加载完成后，获取NODE数据的可访问状态
        loadNodeDataAccessStatus();
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 添加Node数据可访问状态的状态对象
  const nodeIdAccessStatus = ref({}); // runId -> visibleStatus映射
  const requestCompleted = ref(true);

  /**
   * 获取NODE数据的可访问状态
   */
  function loadNodeDataAccessStatus() {
    // 先从表格数据中获取所有记录的ID
    if (tableData.value.length === 0) return;

    // 获取当前页面所有数据的ID
    const ids = tableData.value.map(item => item.id);

    // requestCompleted.value = false;
    // 调用后端接口获取NODE数据的可访问状态
    // getNodeDataAccessStatus(ids)
    //   .then(response => {
    //     const data = response.data || {};
    //
    //     // 更新四种状态映射
    //     nodeIdAccessStatus.value = data || {};
    //   })
    //   .catch(error => {
    //     console.error('Failed to fetch Node data access status:', error);
    //   })
    //   .finally(() => {
    //     requestCompleted.value = true;
    //   });
  }

  // 表头样式处理
  const getHeaderStyle = ({ column }) => {
    const baseStyle = {
      backgroundColor: '#F1F5F9',
      color: '#333333',
      fontWeight: 700,
    };

    // 根据不同区域设置不同的背景色
    if (column.level === 1) {
      if (column.label === 'Omics Data Archive Information') {
        baseStyle.backgroundColor = '#DEDFE0';
      } else if (
        column.label ===
        'Sample Collection Environment Information (Provided by Data Owner)'
      ) {
        baseStyle.backgroundColor = '#D9ECFF';
      } else if (
        column.label === 'Inclusion in Characterized Datasets/Studies'
      ) {
        baseStyle.backgroundColor = '#FAECD8';
      } else if (
        column.label ===
        'Other Sample Metadata (Derived from AI Model Governance)'
      ) {
        baseStyle.backgroundColor = '#E1F3D8';
      }
    }

    return baseStyle;
  };

  const selectLoading = ref(false);
  const selectOption = ref({});

  function remoteSelect(queryStr, field, cb) {
    queryStr = trimStr(queryStr);
    queryParams.value.field = field;
    queryParams.value.selectSearchKeyword = queryStr;
    searchSelectWord(queryParams.value)
      .then(response => {
        selectOption.value[field] = response.data || [];
        if (cb) {
          let results = selectOption.value[field].map(value => ({ value }));
          cb(results);
        }
      })
      .finally(() => {
        selectLoading.value = false;
      });
  }

  let selectRows = ref([]);

  // 计算属性：判断是否有选中的且Analysis Result Exist为Yes的数据
  const hasValidSelection = computed(() => {
    return selectRows.value.some(row => row.hasResult === 'Yes');
  });

  function handleSelectionChange(selection) {
    selectRows.value = selection;
    // console.log(selectRows.value);
  }

  // 确认添加到购物车
  function confirmAddToCart() {
    // 过滤出Analysis Result Exist为Yes的数据
    const validRows = selectRows.value.filter(row => row.hasResult === 'Yes');

    if (validRows.length === 0) {
      proxy.$modal.msgError(
        'Please select at least one item with Analysis Result Exist = Yes to add to cart',
      );
      return;
    }

    let groupName = '';

    if (selectedOption.value === 'new') {
      if (!newGroupName.value.trim()) {
        proxy.$modal.msgError('Please enter a group name');
        return;
      }
      groupName = newGroupName.value.trim();
    } else {
      if (!selectedGroupName.value) {
        proxy.$modal.msgError('Please select an existing group');
        return;
      }
      groupName = selectedGroupName.value;
    }

    // 获取有效选中数据的runIds（只包含Analysis Result Exist为Yes的数据）
    const runIds = validRows.map(row => row.runId);

    // 检查组的条目数量限制（最多30条）
    const existingGroup = cartStore.cartGroups.find(
      group => group.name === groupName,
    );
    if (existingGroup) {
      // 如果是现有组，检查合并后的总数
      const newRunIds = runIds.filter(id => !existingGroup.runIds.includes(id));
      const totalCount = existingGroup.runCount + newRunIds.length;
      if (totalCount > 30) {
        proxy.$modal.msgError(
          `Cannot add ${newRunIds.length} items to group "${groupName}". Maximum 30 items per group allowed. Current: ${existingGroup.runCount}, Adding: ${newRunIds.length}, Total would be: ${totalCount}`,
        );
        return;
      }
    } else {
      // 如果是新组，检查当前选择的数量
      if (runIds.length > 30) {
        proxy.$modal.msgError(
          `Cannot create group with ${runIds.length} items. Maximum 30 items per group allowed.`,
        );
        return;
      }
    }

    // 添加到购物车store
    cartStore.addGroup(groupName, runIds);

    // 关闭弹框并重置表单
    visible.value = false;
    selectedOption.value = 'new';
    newGroupName.value = '';
    selectedGroupName.value = '';

    // 显示成功消息，包含过滤信息
    const totalSelected = selectRows.value.length;
    const validAdded = validRows.length;
    const filtered = totalSelected - validAdded;

    if (filtered > 0) {
      proxy.$modal.msgSuccess(
        `${validAdded} items added to cart successfully. ${filtered} items were filtered out (Analysis Result Exist ≠ Yes).`,
      );
    } else {
      proxy.$modal.msgSuccess('Items added to cart successfully');
    }
  }

  function exportData() {
    if (selectRows.value.length === 0) {
      proxy.$modal.msgError('Please select at least one item to export');
      return;
    }
    proxy.$modal.msgSuccess('Exporting data...');
    const runIds = selectRows.value.map(row => row.runId);

    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(
      // 表格中结果映射
      selectRows.value.map(item => {
        // 创建基础信息对象
        const baseInfo = {
          'Run ID': item.runId,
          'BioProject ID': item.bioProjectId,
          'BioSample ID': item.bioSampleId,
          'Experiment ID': item.experimentId,
          'Omics Type': item.omicsType,
          'Technology Type': item.technologyType,
          'Data Source': item.dataSource,
          'Organism': item.organism,
        };

        // 环境信息对象
        const environmentInfo = selectedRegions.environment
          ? {
              'Latitude': item.latitude,
              'Longitude': item.longitude,
              'Geo loc name': item.geoLocName,
              'Temperature': item.temperature,
              'Salinity': item.salinity,
              'Depth': item.depth,
              'Pressure': item.pressure,
              'pH': item.ph,
            }
          : {};

        // 数据集信息对象
        const datasetsInfo = selectedRegions.datasets
          ? {
              'MASH-Lake': item.mashLake,
              'Mash-Ocean-V2': item.mashOceanV2,
              'Tara-Ocean': item.taraOcean,
              'MASH-ChinaSea': item.mashChinaSea,
              'MEER': item.meer,
            }
          : {};

        // 元数据信息对象
        const metadataInfo = selectedRegions.metadata
          ? {
              'Sampling Substrate': item.samplingSubstrate,
              'Biome': item.biome,
              'Critical Zone': item.criticalZone,
              'Country': item.country,
              'Hydrosphere Type': item.hydrosphereType,
              'Water Body Type': item.waterBodyTypeByClassification,
              'Geolocation': item.waterBodyTypeByGeographic,
              'Water Body Name': item.waterBodyName,
            }
          : {};

        // 合并所有对象并返回
        return {
          ...baseInfo,
          ...environmentInfo,
          ...metadataInfo,
          ...datasetsInfo,
        };
      }),
    );

    // 设置列宽 - 根据实际导出的表头调整
    const colWidths = [
      { wch: 12 }, // Run ID
      { wch: 15 }, // BioProject ID
      { wch: 15 }, // BioSample ID
      { wch: 15 }, // Experiment ID
      { wch: 12 }, // Omics Type
      { wch: 18 }, // Technology Type
      { wch: 10 }, // Data Source
      { wch: 15 }, // Organism
    ];

    // 根据选择的区域添加对应的列宽
    if (selectedRegions.environment) {
      colWidths.push(
        { wch: 12 }, // Latitude
        { wch: 12 }, // Longitude
        { wch: 20 }, // Geo loc name
        { wch: 12 }, // Temperature
        { wch: 10 }, // Salinity
        { wch: 10 }, // Depth
        { wch: 10 }, // Pressure
        { wch: 8 }, // pH
      );
    }

    // 元数据列宽
    if (selectedRegions.metadata) {
      colWidths.push(
        { wch: 20 }, // Sampling Substrate
        { wch: 15 }, // Biome
        { wch: 12 }, // Critical Zone
        { wch: 15 }, // Country
        { wch: 18 }, // Hydrosphere Type
        { wch: 25 }, // Water Body Type (By Classification)
        { wch: 25 }, // Water Body Type (By Geographic)
        { wch: 20 }, // Water Body Name
      );
    }

    // 数据集列宽
    if (selectedRegions.datasets) {
      colWidths.push(
        { wch: 10 }, // MASH-Lake
        { wch: 14 }, // Mash-Ocean-V2
        { wch: 10 }, // Tara-Ocean
        { wch: 15 }, // MASH-Chinasea
        { wch: 10 }, // MEER
      );
    }

    worksheet['!cols'] = colWidths;

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sample Data');

    // 导出 Excel 文件
    XLSX.writeFile(workbook, `MASH_Data_${new Date().getTime()}.xlsx`);
    // 添加访问日志
    doAddVisitsLog(route, VISITS_LOG_FUNC.exportData, { runIds: runIds });
  }

  const showAllData = ref(false);

  const props = {
    expandTrigger: 'hover',
  };

  // 修改loadChartData函数的实现，替换为按指定规则加载图表数据
  // 加载图表数据
  const loadChartData = () => {
    // 使用当前的查询参数加载各个图表数据
    loadAntNestChart(); // 左侧第一张图：dataSource为antNest的technologyType分类
    loadNodeChart(); // 左侧第二张图：dataSource为Node的technologyType分类
    loadwaterBodyTypeByGeographic(); // 右侧第一张图：waterBodyType统计
    loadwaterBodyTypeByClassification(); // 右侧第二张图：waterBodyName统计
  };

  const pieLabelOption = {
    formatter: '{b|{b}}\n{hr|}\n{c} ({d}%)',
    minMargin: 3,
    lineHeight: 10,
    rich: {
      b: {
        fontSize: 12,
        lineHeight: 18,
        color: '#606266',
      },
      hr: {
        borderColor: '#aaa',
        width: '100%',
        borderWidth: 0.5,
        height: 0,
      },
    },
  };

  const pieEmphasisLabelOption = {
    formatter: '{b|{b}}\n{hr|}\n{c} ({d}%)',
    rich: {
      b: {
        fontSize: 13,
        fontWeight: 'bold',
        color: '#303133',
      },
      hr: {
        borderColor: '#777',
        width: '100%',
        borderWidth: 1,
        height: 0,
      },
    },
  };

  // 图表加载状态
  const antNestChartLoading = ref(false);
  const nodeChartLoading = ref(false);
  const waterBodyTypeByGeographicLoading = ref(false);
  const waterBodyTypeByClassificationLoading = ref(false);
  // 管理员邮箱
  const antNestAdminEmail = ref('');

  // 加载dataSource为antNest的technologyType分类图表
  const loadAntNestChart = () => {
    // 克隆当前查询参数，添加dataSource=antNest条件
    const antNestParams = { ...queryParams.value, dataSource: 'AntNest' };

    antNestChartLoading.value = true;
    getChartTechnologyTypeDistribution(antNestParams)
      .then(response => {
        const data = response.data;
        const seriesData = Object.entries(data).map(([name, value]) => ({
          name,
          value,
        }));
        let sum = [...Object.values(data)].reduce((acc, curr) => acc + curr, 0);
        // 设置图表选项，保持原始样式
        charts.antNestChart.setOption({
          title: {
            text: `${formatNumber(sum)} in AntNest`,
            left: 'center',
            top: '0%',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
          },
          series: [
            {
              name: 'AntNest Data',
              type: 'pie',
              radius: ['30%', '50%'], // 缩小饼图大小
              center: ['50%', '50%'],
              avoidLabelOverlap: true,
              label: {
                show: true,
                position: 'outer',
                ...pieLabelOption,
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 14,
                  fontWeight: 'bold',
                  ...pieEmphasisLabelOption,
                },
              },
              labelLine: {
                show: true,
                length: 5,
                length2: 6,
                smooth: 0.1,
              },
              data: seriesData,
            },
          ],
        });
      })
      .finally(() => {
        antNestChartLoading.value = false;
      });
  };

  // 加载dataSource为Node的technologyType分类图表
  const loadNodeChart = () => {
    // 克隆当前查询参数，添加dataSource=NODE条件
    const nodeParams = { ...queryParams.value, dataSource: 'NODE' };

    nodeChartLoading.value = true;
    getChartTechnologyTypeDistribution(nodeParams)
      .then(response => {
        const data = response.data;
        const seriesData = Object.entries(data).map(([name, value]) => ({
          name,
          value,
        }));
        let sum = [...Object.values(data)].reduce((acc, curr) => acc + curr, 0);

        // 设置图表选项，保持原始样式
        charts.nodeChart.setOption({
          title: {
            text: `${formatNumber(sum)} in NODE`,
            left: 'center',
            top: '0%',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
          },
          series: [
            {
              name: 'NODE Data',
              type: 'pie',
              radius: ['30%', '50%'], // 缩小饼图大小
              center: ['50%', '50%'],
              avoidLabelOverlap: true,
              label: {
                show: true,
                position: 'outer',
                ...pieLabelOption,
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 14,
                  fontWeight: 'bold',
                  ...pieEmphasisLabelOption,
                },
              },
              labelLine: {
                show: true,
                length: 5,
                length2: 6,
                smooth: 0.1,
              },
              data: seriesData,
            },
          ],
        });
      })
      .finally(() => {
        nodeChartLoading.value = false;
      });
  };

  // 加载水体类型分布图表
  const loadwaterBodyTypeByGeographic = () => {
    waterBodyTypeByGeographicLoading.value = true;
    getChartWaterBodyTypeDistribution(queryParams.value)
      .then(response => {
        const data = response.data;
        const waterBodyTypes = Object.keys(data);
        const waterBodyTypeCounts = Object.values(data);

        // 按Categories分类整理数据
        const categorizedData = {};
        const allItems = [];
        const categoryColors = {};

        // 初始化分类数据
        Object.keys(waterBodyTypeCategories).forEach(category => {
          categorizedData[category] = [];
          categoryColors[category] = waterBodyTypeCategories[category].color;
        });

        // 按每个分类的items顺序整理数据
        Object.keys(waterBodyTypeCategories).forEach(category => {
          waterBodyTypeCategories[category].items.forEach(item => {
            const index = waterBodyTypes.indexOf(item);
            if (index !== -1) {
              categorizedData[category].push({
                name: item,
                value: waterBodyTypeCounts[index],
                category: category,
              });
              allItems.push(item);
            }
          });
        });

        // 找出未被任何分类包含的项目，归入unspecified
        waterBodyTypes.forEach((type, index) => {
          if (!allItems.includes(type)) {
            categorizedData.unspecified.push({
              name: type,
              value: waterBodyTypeCounts[index],
              category: 'unspecified',
            });
          }
        });

        // 合并所有数据按分类顺序
        const sortedData = [];
        const sortedLabels = [];

        Object.keys(waterBodyTypeCategories).forEach(category => {
          categorizedData[category].forEach(item => {
            sortedData.push(item);
            sortedLabels.push(item.name);
          });
        });

        // 为每个分类创建一个系列
        const series = Object.keys(waterBodyTypeCategories).map(category => {
          return {
            name: category,
            type: 'bar',
            stack: 'total',
            barMaxWidth: '15',
            itemStyle: {
              color: waterBodyTypeCategories[category].color,
            },
            // 为每个标签填充数据，如果不属于此分类则为空字符串
            data: sortedLabels.map(label => {
              const found = categorizedData[category].find(
                item => item.name === label,
              );
              return found ? found.value : '';
            }),
          };
        });

        // 设置图表选项
        const waterBodyTypeByGeographicOption = {
          title: {
            text: 'By Water body type',
            left: 'center',
            top: '0%',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          tooltip: {},
          legend: {
            data: Object.keys(waterBodyTypeCategories),
            orient: 'horizontal',
            bottom: 0,
            left: 'center',
            itemWidth: 15,
            itemHeight: 10,
            selectedMode: false,
          },
          grid: {
            left: '3%',
            right: '4%',
            top: '8%',
            bottom: '8%',
            containLabel: true,
          },
          dataZoom: [
            {
              type: 'inside', // 内置型数据区域缩放组件
              start: 0,
              end: 100,
              zoomOnMouseWheel: true, // 是否允许通过鼠标滚轮缩放
              moveOnMouseWheel: false, // 是否允许通过鼠标滚轮平移
            },
          ],
          xAxis: {
            type: 'category',
            data: sortedLabels,
            axisLabel: {
              interval: 0,
              rotate: 45,
            },
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              rotate: 0,
            },
            axisLine: {
              show: true,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: true,
            },
          },
          series: series,
        };

        charts.waterBodyTypeByGeographic.setOption(
          waterBodyTypeByGeographicOption,
        );
      })
      .finally(() => {
        waterBodyTypeByGeographicLoading.value = false;
      });
  };

  // 加载水体名称分布图表
  const loadwaterBodyTypeByClassification = () => {
    // 创建自定义API请求，获取按waterBodyName分组的数据
    waterBodyTypeByClassificationLoading.value = true;
    getWaterBodyNameDistribution(queryParams.value)
      .then(response => {
        const data = response.data;
        const geoDistLocations = Object.keys(data);
        const geoDistCounts = Object.values(data);

        // 按geographicCategories分类整理数据
        const categorizedData = {};
        const allItems = [];
        const categoryColors = {};

        // 初始化分类数据
        Object.keys(geographicCategories).forEach(category => {
          categorizedData[category] = [];
          categoryColors[category] = geographicCategories[category].color;
        });

        // 按每个分类的items顺序整理数据
        Object.keys(geographicCategories).forEach(category => {
          geographicCategories[category].items.forEach(item => {
            const index = geoDistLocations.indexOf(item);
            if (index !== -1) {
              categorizedData[category].push({
                name: item,
                value: geoDistCounts[index],
                category: category,
              });
              allItems.push(item);
            }
          });
        });

        // 找出未被任何分类包含的项目，归入unspecified
        geoDistLocations.forEach((location, index) => {
          if (!allItems.includes(location)) {
            categorizedData.unspecified.push({
              name: location,
              value: geoDistCounts[index],
              category: 'unspecified',
            });
          }
        });

        // 合并所有数据按分类顺序
        const sortedData = [];
        const sortedLabels = [];

        Object.keys(geographicCategories).forEach(category => {
          categorizedData[category].forEach(item => {
            sortedData.push(item);
            sortedLabels.push(item.name);
          });
        });

        // 为每个分类创建一个系列
        const series = Object.keys(geographicCategories).map(category => {
          return {
            name: category,
            type: 'bar',
            stack: 'total',
            barMaxWidth: '15',
            itemStyle: {
              color: geographicCategories[category].color,
            },
            // 为每个标签填充数据，如果不属于此分类则为空字符串
            data: sortedLabels.map(label => {
              const found = categorizedData[category].find(
                item => item.name === label,
              );
              return found ? found.value : '';
            }),
          };
        });

        // 设置图表选项
        const geoDistChartOption = {
          title: {
            text: 'By Geolocation',
            left: 'center',
            top: '0%',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          tooltip: {},
          legend: {
            data: Object.keys(geographicCategories),
            orient: 'horizontal',
            bottom: 10,
            left: 'center',
            itemWidth: 15,
            itemHeight: 10,
            selectedMode: false,
          },
          grid: {
            left: '3%',
            right: '4%',
            top: '8%',
            bottom: '8%',
            containLabel: true,
          },
          dataZoom: [
            {
              type: 'inside', // 内置型数据区域缩放组件
              start: 0,
              end: 100,
              zoomOnMouseWheel: true, // 是否允许通过鼠标滚轮缩放
              moveOnMouseWheel: false, // 是否允许通过鼠标滚轮平移
            },
          ],
          xAxis: {
            type: 'category',
            data: sortedLabels,
            axisLabel: {
              interval: 0,
              rotate: 45,
            },
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              rotate: 0,
            },
            axisLine: {
              show: true,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: true,
            },
          },
          series: series,
        };

        charts.waterBodyTypeByClassification.setOption(geoDistChartOption);
      })
      .finally(() => {
        waterBodyTypeByClassificationLoading.value = false;
      });
  };

  // 处理窗口大小变化
  const handleResize = () => {
    // 添加一个小延时确保DOM已更新
    setTimeout(() => {
      Object.values(charts).forEach(chart => {
        if (chart) {
          chart.resize();
        }
      });
      // 调整地图尺寸
      if (map.value) {
        map.value.invalidateSize();
      }
    }, 300);
  };

  let map = ref({});

  function initMap() {
    // 检查所有地理数据是否已加载
    if (!oceanData.value || !lakesData.value || !riversData.value) {
      console.warn('地理数据尚未加载完成，正在重新获取...');
      fetchGeoData().then(success => {
        if (success) {
          initMap();
        } else {
          proxy.$modal.msgError('加载地图数据失败，请刷新页面重试');
        }
      });
      return;
    }

    var latlng = L.latLng(30, 0);

    map.value = L.map(showAllData.value ? 'filterMap' : 'dataMap', {
      // crs: L.CRS.Simple,
      center: latlng,
      zoom: 2,
      minZoom: 2, // 设置最小缩放级别为 10
      maxZoom: 18, // 设置最小缩放级别为 10
      // layers: [tiles],
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.value.createPane('oceanPane');
    map.value.createPane('riverPane');
    map.value.createPane('pointsPane');

    map.value.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.value.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.value.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });
    const oceanLayer = L.geoJson
      .vt(oceanData.value, {
        maxZoom: 18,
        tolerance: 3,
        style: {
          fillColor: '#1C4F80',
          weight: 1,
          opacity: 1,
          color: 'rgba(0, 0, 0, 0)',
          fillOpacity: 1,
          pane: 'oceanPane',
          renderer: canvasRenderer,
        },
      })
      .addTo(map.value);

    // 遍历海洋的features，创建标签
    oceanData.value.features.forEach(feature => {
      let labelLatLng;
      // 根据特征名称选择标签位置
      if (feature.properties.name === 'North Pacific Ocean') {
        labelLatLng = L.latLng(30, -150);
      } else if (feature.properties.name === 'South Pacific Ocean') {
        labelLatLng = L.latLng(-30, -140);
      } else if (feature.properties.name === 'Baltic Sea') {
        labelLatLng = L.latLng(59, 21);
      } else if (feature.properties.name === 'Southern Ocean') {
        labelLatLng = L.latLng(-64, 20);
      } else {
        // 默认使用中心点
        const bounds = L.geoJSON(feature).getBounds();
        labelLatLng = bounds.getCenter();
      }

      // 创建一个标记
      var label = L.marker(labelLatLng, {
        icon: L.divIcon({
          className: 'ocean-label',
          html: feature.properties.name,
          iconSize: [100, 20],
        }),
      });

      label.addTo(map.value); // 将标签添加到地图
    });

    // 添加 lake 图层（矢量瓦片方式）
    const lakeLayer = L.geoJson
      .vt(lakesData.value, {
        maxZoom: 14,
        tolerance: 3,
        style: {
          fillColor: '#9ABAE7',
          weight: 1,
          opacity: 1,
          color: 'rgba(0, 0, 0, 0)',
          fillOpacity: 1,
          pane: 'oceanPane',
          renderer: canvasRenderer,
        },
      })
      .addTo(map.value);

    // 遍历 lakes 的 features，创建标签
    lakesData.value.features.forEach(feature => {
      const bounds = L.geoJSON(feature).getBounds();
      const center = bounds.getCenter();

      const label = L.marker(center, {
        icon: L.divIcon({
          iconSize: [100, 20],
          className: 'lake-label clickable-label',
          html: map.value.getZoom() > 4 ? feature.properties.Name : '',
        }),
      });

      // 为湖泊标签添加点击事件
      label.on('click', function () {
        if (feature.properties.Name) {
          queryParams.value.waterBodyName = feature.properties.Name;
          getDataList(); // 点击后立即查询数据
        }
      });

      label.addTo(map.value);

      // 控制缩放时是否显示 label
      map.value.on('zoomend', () => {
        const zoom = map.value.getZoom();
        label.setIcon(
          L.divIcon({
            className: 'lake-label clickable-label',
            html: zoom > 4 ? feature.properties.Name : '',
          }),
        );
      });
    });

    // 使用 geoJson.vt 渲染瓦片图层
    const riverLayer = L.geoJson
      .vt(riversData.value, {
        maxZoom: 14,
        tolerance: 3,
        style: {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          renderer: canvasRenderer,
          pane: 'riverPane',
        },
      })
      .addTo(map.value);

    // 遍历原始 GeoJSON feature，添加标签
    riversData.value.features.forEach(feature => {
      const layerBounds = L.geoJSON(feature).getBounds();

      const label = L.marker(layerBounds.getCenter(), {
        icon: L.divIcon({
          iconSize: [100, 20],
          className: 'lake-label clickable-label',
          html: map.value.getZoom() > 4 ? feature.properties.name : '',
        }),
      });

      // 为河流标签添加点击事件
      label.on('click', function () {
        if (feature.properties.name) {
          queryParams.value.waterBodyName = feature.properties.name;
          getDataList(); // 点击后立即查询数据
        }
      });

      label.addTo(map.value);

      map.value.on('zoomend', () => {
        const riverZoom = map.value.getZoom();
        label.setIcon(
          L.divIcon({
            className: 'lake-label clickable-label',
            html: riverZoom > 4 ? feature.properties.name : '',
          }),
        );
      });
    });
  }

  // 解析经纬度字符串
  function parseLatLon(latLonStr) {
    if (!latLonStr || latLonStr === '无') return null;

    try {
      // 移除所有空格，然后按逗号分割
      const parts = latLonStr.replace(/\s+/g, '').split(',');
      if (parts.length === 2) {
        const lat = parseFloat(parts[0]);
        const lon = parseFloat(parts[1]);
        if (!isNaN(lat) && !isNaN(lon)) {
          return [lat, lon];
        }
      }
    } catch (error) {
      console.error('Error parsing lat/lon:', error);
    }

    return null;
  }

  // NODE Dialog related
  const nodeDialogVisible = ref(false);
  const nodeData = ref([]);
  const nodeCodeUrl = ref('');
  const nodeCredentials = reactive({
    username: '',
    password: '',
    typeNos: [],
    description: '',
    captchaCode: '',
    captchaId: '',
  });

  const nodeFormRef = ref(null);

  const nodeFormRules = reactive({
    username: [
      {
        required: true,
        message: 'Please enter your NODE username',
        trigger: 'blur',
      },
    ],
    password: [
      {
        required: true,
        message: 'Please enter your NODE password',
        trigger: 'blur',
      },
    ],
    captchaCode: [
      {
        required: true,
        message: 'Please enter the verification code',
        trigger: 'blur',
      },
    ],
  });

  const selectedNodeData = computed(() => {
    return selectRows.value.filter(item => item.dataSource === 'NODE');
  });
  const selectedAntNestData = computed(() => {
    return selectRows.value.filter(item => item.dataSource === 'AntNest');
  });

  let selectedNodeProjects = ref([]);
  // 处理表格选择变化
  const handleNodeSelectionChange = selection => {
    selectedNodeProjects.value = selection;
  };

  const hasSelectedUnAccessible = computed(() => {
    return selectedNodeProjects.value.some(
      item => item.visibleStatus === 'Unaccessible',
    );
  });

  const canSubmit = computed(() => {
    // 如果没有选择任何项目，不能提交
    if (selectedNodeProjects.value.length === 0) {
      return false;
    }

    // 如果选择了不可访问项目，要求填写凭证
    if (hasSelectedUnAccessible.value) {
      return nodeCredentials.username && nodeCredentials.password;
    }

    // 只选择了可访问项目，可以直接提交
    return true;
  });

  const showNodeDialog = () => {
    proxy.$modal.loading('Loading');
    let ids = selectedNodeData.value.map(x => x.experimentId);
    if (ids.length <= 0) {
      return;
    }
    getNodeDataByExperimentIds({ typeNos: ids })
      .then(res => {
        const order = ['Unaccessible', 'Accessible'];
        nodeData.value = res.data.sort((a, b) => {
          return (
            order.indexOf(a.visibleStatus) - order.indexOf(b.visibleStatus)
          );
        });
        nodeDialogVisible.value = true;
        // 清空选择项目
        selectedNodeProjects.value = [];
        // 获取验证码
        getNodeCode();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  };

  const nodeSubmitLoading = ref(false);

  const debouncedSubmitNodeApplication = debounce(
    () => {
      // 使用表单验证
      proxy.$refs.nodeFormRef.validate(valid => {
        if (valid) {
          // 获取选中的项目ID，只包括用户手动选中的项目

          nodeCredentials.typeNos = selectedNodeProjects.value.map(
            x => x.expNo,
          );

          // 设置加载状态
          nodeSubmitLoading.value = true;
          proxy.$modal.loading('Submitting application...');
          // 添加日志
          doAddVisitsLog(
            route,
            VISITS_LOG_FUNC.applyNodeData,
            toRaw(nodeCredentials),
          );
          // 调用API
          applyNodeData(nodeCredentials)
            .then(response => {
              proxy.$modal.alertSuccess(
                'Your application has been submitted successfully, you can view this application in the User Center of Node(<a href="https://www.biosino.org/node/userCenter/MyRequest" class="text-primary" target="_blank">https://www.biosino.org/node/userCenter/MyRequest</a>)',
                true,
              );
              nodeDialogVisible.value = false;
              // 重置表单
              proxy.$refs.nodeFormRef.resetFields();

              selectedNodeProjects.value = [];
            })
            .finally(() => {
              // 无论成功还是失败，都重置加载状态
              nodeSubmitLoading.value = false;
              // 更新验证码
              getNodeCode();
              proxy.$modal.closeLoading();
            });
        }
      });
    },
    500,
    true,
  );

  const submitNodeApplication = () => {
    debouncedSubmitNodeApplication();
  };

  // 重置查询条件
  const resetQuery = () => {
    // 重置查询参数
    queryParams.value = {
      runId: '',
      bioProjectId: '',
      bioSampleId: '',
      experimentId: '',
      omicsType: '',
      technologyType: '',
      latitudeStart: sliderLatitude.value[0],
      latitudeEnd: sliderLatitude.value[1],
      longitudeStart: sliderLongitude.value[0],
      longitudeEnd: sliderLongitude.value[1],
      geoLocName: '',
      temperature: '',
      temperatureStart: '',
      temperatureEnd: '',
      salinity: '',
      salinityStart: '',
      salinityEnd: '',
      depth: '',
      depthStart: '',
      depthEnd: '',
      pressure: '',
      pressureStart: '',
      pressureEnd: '',
      ph: '',
      phStart: '',
      phEnd: '',
      mashLake: '',
      mashOceanV2: '',
      taraOcean: '',
      mashChinaSea: '',
      meer: '',
      samplingSubstrate: '',
      biome: '',
      criticalZone: '',
      country: '',
      hydrosphereType: '',
      waterBodyTypeByClassification: '',
      waterBodyTypeByGeographic: '',
      waterBodyName: '',
      waterBodyType: '',
      pageNum: 1,
      pageSize: 20,
    };
    getDataList();
  };

  // Ant Nest Dialog related
  const antNestDialogVisible = ref(false);
  const antNestCodeUrl = ref('');
  const antNestForm = reactive({
    title: '',
    name: '',
    institution: '',
    email: '',
    requestText: '',
    runIds: [],
    captchaCode: '',
    captchaId: '',
  });
  const antNestSubmitLoading = ref(false);

  const antNestFormRules = reactive({
    title: [
      { required: true, message: 'Please enter the title', trigger: 'blur' },
      {
        min: 5,
        max: 100,
        message: 'Length should be between 5 and 100',
        trigger: 'blur',
      },
    ],
    name: [
      { required: true, message: 'Please enter your name', trigger: 'blur' },
      {
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Name can only contain letters and spaces',
        trigger: 'blur',
      },
    ],
    institution: [
      {
        required: true,
        message: 'Please enter your institution',
        trigger: 'blur',
      },
      {
        min: 2,
        max: 100,
        message: 'Length should be between 2 and 100',
        trigger: 'blur',
      },
    ],
    email: [
      { required: true, message: 'Please enter your email', trigger: 'blur' },
      {
        type: 'email',
        message: 'Please enter a valid email address',
        trigger: 'blur',
      },
    ],
    captchaCode: [
      {
        required: true,
        message: 'Please enter the verification code',
        trigger: 'blur',
      },
    ],
  });

  const antNestFormRef = ref(null);

  const canSubmitAntNest = computed(() => {
    return (
      antNestForm.title.trim() !== '' &&
      antNestForm.name.trim() !== '' &&
      antNestForm.institution.trim() !== '' &&
      antNestForm.email.trim() !== ''
    );
  });

  const debouncedSubmitAntNestApplication = debounce(
    () => {
      // 使用proxy.$refs进行表单验证
      proxy.$refs.antNestFormRef.validate(valid => {
        if (valid) {
          // 准备项目ID数据
          const runIds = selectedAntNestData.value.map(row => row.runId);

          // 创建请求DTO
          antNestForm.runIds = runIds;

          // 设置加载状态
          antNestSubmitLoading.value = true;
          proxy.$modal.loading('Submitting application...');

          // 添加访问日志
          doAddVisitsLog(
            route,
            VISITS_LOG_FUNC.applyAntNestData,
            toRaw(antNestForm),
          );

          // 调用API提交申请
          applyAntNestData(antNestForm)
            .then(response => {
              proxy.$modal.alertSuccess(
                'Your application has been submitted successfully, AntNest administrator will process your application as soon as possible!',
              );
              antNestDialogVisible.value = false;
              // 使用resetForm重置表单
              proxy.$refs.antNestFormRef.resetFields();
              selectedAntNestData.value = [];
            })
            .finally(() => {
              // 无论成功还是失败，都重置加载状态
              antNestSubmitLoading.value = false;
              // 更新验证码
              getAntNestCode();
              proxy.$modal.closeLoading();
            });
        }
      });
    },
    500,
    true,
  );

  const submitAntNestApplication = () => {
    debouncedSubmitAntNestApplication();
  };

  // 跳转到Biota页面
  function navigateToBiota() {
    proxy.$modal.msgWarning('It will come soon');
    /*if (selectRows.value.length === 0) {
proxy.$modal.msgError('Please select at least one item to view in Biota');
return;
}

// 收集所有选中行的ID
const selectedIds = selectRows.value.map(row => row.runId);

// 跳转到biota路径，同时传递选中的ID作为查询参数
router.push({
path: '/biota',
query: {
ids: selectedIds.join(','),
},
});*/
  }

  // 获取NODE验证码
  const getNodeCode = () => {
    getCaptchaImg().then(res => {
      nodeCodeUrl.value = 'data:image/jpeg;base64,' + res.img;
      nodeCredentials.captchaId = res.uuid;
      nodeCredentials.captchaCode = '';
    });
  };

  // 获取Ant Nest验证码
  const getAntNestCode = () => {
    getCaptchaImg().then(res => {
      antNestCodeUrl.value = 'data:image/jpeg;base64,' + res.img;
      antNestForm.captchaId = res.uuid;
      antNestForm.captchaCode = '';
    });
  };

  // 在打开Ant Nest对话框时获取验证码
  watch(antNestDialogVisible, val => {
    if (val) {
      getAntNestCode();
    }
  });

  // 在打开Node对话框时获取验证码
  watch(nodeDialogVisible, val => {
    if (val) {
      getNodeCode();
    }
  });
</script>

<style lang="scss" scoped>
  .submit-page {
    padding: 120px 0 45px 0;
  }

  // 添加chart-item样式
  .chart-item {
    height: 300px;
    width: 100%;
    background-color: transparent;
    border: none;
    box-shadow: none;
    padding: 10px;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  .filter-svg {
    width: 18px;
    height: 18px;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.clickable-label) {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
      text-decoration: underline;
      font-weight: bold;
    }
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  .notice-box {
    display: flex;
    align-items: flex-start;
    background-color: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;

    .notice-icon {
      color: #0ea5e9;
      font-size: 24px;
      margin-right: 12px;
      margin-top: 2px;
    }

    .notice-content {
      h4 {
        color: #0369a1;
        margin: 0 0 4px 0;
        font-size: 16px;
      }

      p {
        color: #475569;
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }

  .node-table {
    margin-bottom: 24px;
  }

  .status-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    padding: 4px 0;

    &.accessible {
      color: #22c55e;

      .el-icon {
        font-size: 18px;
      }
    }

    &.unaccessible {
      color: #ef4444;

      .el-icon {
        font-size: 18px;
      }
    }

    span {
      font-size: 14px;
    }
  }

  .login-section {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 24px;
    margin-top: 32px;
  }

  .login-form {
    margin: 0 auto;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #1e293b;
    }

    .login-input {
      :deep(.el-input__wrapper) {
        padding-left: 12px;
      }

      :deep(.el-input__prefix) {
        margin-right: 8px;
        color: #64748b;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
  }

  .container-fluid {
    max-width: 1640px !important;
  }

  /* 新增的样式 */
  .map-container {
    max-width: 100%;
    height: 530px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
  }

  .chart-container-left,
  .chart-container-right {
    height: 580px;
    display: flex;
    flex-direction: column;
  }

  .pie-chart {
    flex: 1;
    min-height: 0;
    background-color: transparent;
    border: none;
    box-shadow: none;
  }

  .bar-chart {
    flex: 1;
    min-height: 0;
    background-color: transparent;
    border: none;
    box-shadow: none;
    padding: 10px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .table-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 5px;
    color: #1e7cb2;
    font-size: 16px;
    justify-content: flex-end;

    .el-icon {
      font-size: 16px;
      color: #1e7cb2;
    }
  }

  .region-control-row {
    margin-bottom: 1rem;

    .button-group {
      display: flex;
      gap: 8px;
      height: 100%;
      align-items: center;
    }
  }

  .region-selector {
    float: left;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;

    :deep(.el-checkbox) {
      height: auto;
      padding: 8px 12px;
      border-radius: 4px;
      margin: 0;

      .el-checkbox__label {
        font-size: 16px;
        color: #333;
      }

      &:nth-child(1) {
        width: 180px;
        background-color: #dedfe0;
      }

      &:nth-child(2) {
        width: 210px;
        background-color: #d9ecff;
      }

      &:nth-child(3) {
        width: 150px;
        background-color: #e1f3d8;
      }

      &:nth-child(4) {
        width: 210px;
        background-color: #faecd8;
      }
    }
  }

  .table-header {
    .el-input,
    .el-select {
      margin-top: 5px;
      width: 100%;
    }
  }

  :deep(.el-table) {
    // 设置表头样式
    .el-table__header {
      th {
        padding: 8px;

        .cell {
          padding: 0;
        }
      }
    }

    // 设置不同区域的表头背景色
    .el-table__header-row {
      th.is-leaf {
        background-color: #f8fafc;
      }
    }
  }

  .filter-row {
    display: flex;
    align-items: center;
    gap: 8px;

    .filter-controls {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .el-autocomplete-suggestion__wrap {
    z-index: 999;
  }

  .ant-nest-form {
    padding: 24px;
    background-color: #f8fafc;
    border-radius: 8px;
    margin-top: 20px;

    .form-input {
      :deep(.el-input__wrapper) {
        padding-left: 12px;
      }

      :deep(.el-input__prefix) {
        margin-right: 8px;
        color: #64748b;
      }
    }
  }

  .captcha-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .login-code {
    img {
      cursor: pointer;
      vertical-align: middle;
      height: 40px;
      border-radius: 4px;
    }
  }

  :deep(.el-scrollbar__bar.is-horizontal) > div {
    height: 140% !important;
  }

  :deep(.el-scrollbar__bar.is-vertical) > div {
    width: 140% !important;
  }

  .add-to-cart-content {
    .group-selection {
      margin-bottom: 20px;

      .group-options {
        width: 100%;

        .option-item {
          margin-bottom: 16px;
          width: 100%;

          &:last-child {
            margin-bottom: 0;
          }

          .new-group-input,
          .existing-groups {
            margin-left: 24px;
            margin-top: 8px;
          }
        }
      }
    }

    .dialog-footer {
      margin-top: 0;
      border-top: none !important;
    }

    :deep(.el-radio) {
      width: 100%;

      .el-radio__label {
        width: 100%;
      }
    }
  }
</style>
<style>
  .hide-select-all .el-table__header-wrapper .el-checkbox {
    display: none;
  }
</style>
