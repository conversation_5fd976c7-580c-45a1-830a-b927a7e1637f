package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.BiogeographyCreateDTO;
import org.biosino.lf.mash.mashweb.dto.DiversityCompareCreateDTO;
import org.biosino.lf.mash.mashweb.dto.GroupInfoDTO;
import org.biosino.lf.mash.mashweb.dto.SamplesQueryDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesRepository;
import org.biosino.lf.mash.mashweb.util.RuntimeUtils;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.biosino.lf.mash.mashweb.util.CsvReaderUtils.readLinesToListStr;

/**
 * <AUTHOR> Li
 * @date 2025/7/21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DiversityService extends AnalysisBaseService {

    private final SamplesRepository samplesRepository;
    private final FileProperties fileProperties;


    public String createBiogeography(BiogeographyCreateDTO paramsDTO) {
        String id = IdUtil.getSnowflakeNextIdStr();

        // 先生成输入文件
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(id));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(id));

        // 将数据写入input目录
        File speciesNameFile = FileUtil.writeUtf8Lines(paramsDTO.getSpeciesNames(), FileUtil.file(inputDir, "species_name_list.txt"));
        // 如果是条件，就查询
        List<String> selectRunIds;
        if (CollUtil.isNotEmpty(paramsDTO.getRunIds())) {
            selectRunIds = paramsDTO.getRunIds();
        } else {
            SamplesQueryDTO queryDTO = paramsDTO.getQueryDTO();
            selectRunIds = samplesRepository.findDistinctRunId(queryDTO);
        }
        File sampleListFile = FileUtil.writeUtf8Lines(selectRunIds, FileUtil.file(inputDir, "sample_list.txt"));


        String abundanceFilePath = fileProperties.getBaseDataDir() + "/Abundance_Table/" + paramsDTO.getDomain() + "." + paramsDTO.getLevel() + ".percent.csv";

        String cmd = StrUtil.format("python {} {} {} {} {}",
                fileProperties.getScriptDir() + "/process_species_data.py",
                abundanceFilePath,
                speciesNameFile.getAbsolutePath(),
                sampleListFile.getAbsolutePath(),
                outputDir.getAbsolutePath()
        );

        int exitCode = RuntimeUtils.execForExitCode(cmd, id);
        if (exitCode != 0) {
            throw new ServiceException("分析失败");
        }
        // 开始写入结果文件，方便后续
        for (String speciesName : paramsDTO.getSpeciesNames()) {
            File file = FileUtil.file(outputDir, speciesName + ".tsv");
            if (!file.exists()) {
                continue;
            }
            CsvReader reader = CsvUtil.getReader(CsvReadConfig.defaultConfig().setFieldSeparator('\t'));
            List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);

            // 查询数据
            List<String> runIds = list.stream().map(SamplesBioGeographyVO::getRunId).toList();


            List<Samples> samples = samplesRepository.findByRunIdIn(runIds);

            // 转成runId to Sample Map
            Map<String, Samples> runIdToSampleMap = samples.stream().collect(Collectors.toMap(Samples::getRunId, Function.identity(), (x, y) -> y));

            List<SamplesBioGeographyVO> result = list.stream().peek(x -> {
                if (runIdToSampleMap.containsKey(x.getRunId())) {
                    Samples item = runIdToSampleMap.get(x.getRunId());
                    // BeanUtil.copyProperties(item, x);
                    if (item.getLatitude() != null) {
                        x.setLatitude(String.valueOf(item.getLatitude()));
                    }
                    if (item.getLongitude() != null) {
                        x.setLongitude(String.valueOf(item.getLongitude()));
                    }
                }
            }).toList();

            // 将结果写入到结果文件
            File resultFile = FileUtil.file(outputDir, speciesName + "_result.csv");
            CsvWriter writer = CsvUtil.getWriter(FileUtil.getWriter(resultFile, Charset.defaultCharset(), false));
            writer.writeBeans(result);
            writer.close();
        }

        return id;
    }

    public List<String> getSpeciesNameList(String taskId) {
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(taskId));
        File sampleListFile = FileUtil.file(inputDir, "species_name_list.txt");

        return FileUtil.readUtf8Lines(sampleListFile);
    }

    public String createDiversity(DiversityCompareCreateDTO paramsDTO) {
        String id = IdUtil.getSnowflakeNextIdStr();

        // 先生成输入文件
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(id));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(id));

        File groupFile = FileUtil.file(inputDir, "Group.txt");
        File groupColorFile = FileUtil.file(inputDir, "group_color.tsv");
        List<String> groupInfo = new ArrayList<>();
        groupInfo.add("sample_name\tgroup");
        List<String> groupColor = new ArrayList<>();
        groupColor.add("Group\tColor");
        int i = 0;

        List<String> runIds = new ArrayList<>();
        for (GroupInfoDTO groupInfoDTO : paramsDTO.getGroupInfos()) {
            if (CollUtil.isNotEmpty(groupInfoDTO.getRunIds())) {
                runIds.addAll(groupInfoDTO.getRunIds());
                for (String runId : groupInfoDTO.getRunIds()) {
                    groupInfo.add(StrUtil.format("{}\t{}", runId, groupInfoDTO.getGroupName()));
                }
            } else if (StrUtil.isNotBlank(groupInfoDTO.getWaterBodyTypeByClassification())) {
                String waterBodyTypeByClassification = groupInfoDTO.getWaterBodyTypeByClassification();
                List<String> ids = samplesRepository.findByWaterBodyTypeByClassificationLimit30(waterBodyTypeByClassification);
                if (CollUtil.isEmpty(ids)) {
                    throw new ServiceException(StrUtil.format("未找到 {} 下有分析结果的样本", waterBodyTypeByClassification));
                }
                runIds.addAll(ids);
                for (String runId : ids) {
                    groupInfo.add(StrUtil.format("{}\t{}", runId, groupInfoDTO.getGroupName()));
                }
            } else if (StrUtil.isNotBlank(groupInfoDTO.getWaterBodyTypeByGeographic())) {
                String waterBodyTypeByGeographic = groupInfoDTO.getWaterBodyTypeByGeographic();
                List<String> ids = samplesRepository.findByWaterBodyTypeByGeographicLimit30(waterBodyTypeByGeographic);
                if (CollUtil.isEmpty(ids)) {
                    throw new ServiceException(StrUtil.format("未找到 {} 下有分析结果的样本", waterBodyTypeByGeographic));
                }
                runIds.addAll(ids);
                for (String runId : ids) {
                    groupInfo.add(StrUtil.format("{}\t{}", runId, groupInfoDTO.getGroupName()));
                }
            } else {
                throw new ServiceException("参数错误");
            }
            groupColor.add(StrUtil.format("{}\t{}", groupInfoDTO.getGroupName(), GROUP_COLOR[i]));
            i++;
        }

        // 拷贝文件
        runIds.forEach(x -> {
            File sourceFile = FileUtil.file(fileProperties.getBaseDataDir(), "bracken_16_species", StrUtil.format("{}.k2_pluspf_16gb_20220908_bracken_species.report", x));
            File targetFile = FileUtil.file(inputDir, "bracken_16_species", StrUtil.format("{}.k2_pluspf_16gb_20220908_bracken_species.report", x));
            FileUtil.copy(sourceFile, targetFile, true);
        });

        FileUtil.writeUtf8Lines(groupInfo, groupFile);
        FileUtil.writeUtf8Lines(groupColor, groupColorFile);

        FileUtil.mkdir(outputDir);

        String cmd = StrUtil.format("bash {} {} {} {} {} {}",
                fileProperties.getScriptDir() + "/mash_code/run.kraken.sh",
                fileProperties.getScriptDir() + "/mash_code",
                inputDir.getAbsolutePath(),
                outputDir.getAbsolutePath(),
                paramsDTO.getDomain(),
                paramsDTO.getLevel()
        );

        int exitCode = RuntimeUtils.execForExitCode(cmd);
        if (exitCode != 0) {
            throw new ServiceException("分析失败");
        }
        if (groupColor.size() > 2) {
            String lefseCmd = StrUtil.format("bash {} {} {} {} {} {}",
                    fileProperties.getScriptDir() + "/mash_code/run.lefse.sh",
                    fileProperties.getScriptDir() + "/mash_code",
                    inputDir.getAbsolutePath(),
                    outputDir.getAbsolutePath(),
                    paramsDTO.getDomain(),
                    paramsDTO.getLevel()
            );
            int lefseExitCode = RuntimeUtils.execForExitCode(lefseCmd);
            if (lefseExitCode != 0) {
                throw new ServiceException("分析失败");
            }
        }
        return id;
    }

    public Object barPlotData(String taskId, String domain, String level) {
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(taskId));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File groupFile = FileUtil.file(inputDir, "Group.txt");
        List<List<String>> groupFileLines = readLinesToListStr(groupFile, '\t');
        List<String> groups = groupFileLines.stream().map(x -> x.get(1)).distinct().toList();

        File file = FileUtil.file(outputDir, "Barplot_Table", StrUtil.format("Barplot.{}.{}.csv", domain, level));
        File legendFile = FileUtil.file(outputDir, "Barplot_Table", StrUtil.format("Barlegend.{}.{}.csv", domain, level));

        List<List<String>> dataList = readLinesToListStr(file);
        List<List<String>> sortedList = dataList.stream().sorted(Comparator.comparing(x -> x.get(0))).toList();
        Map<String, List<List<String>>> map = sortedList.stream().collect(Collectors.groupingBy(x -> x.get(1), LinkedHashMap::new, Collectors.toList()));
        Set<String> group = map.keySet();
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        for (String g : group) {
            List<List<String>> list = map.get(g);
            LinkedHashMap<String, List<List<String>>> taxMap = list.stream().collect(Collectors.groupingBy(x -> x.get(2), LinkedHashMap::new, Collectors.toList()));
            data.set(g, taxMap);
        }
        result.set("groups", groups);
        result.set("data", MapUtil.sort(data));
        result.set("legend", readLinesToListStr(legendFile).stream().map(x -> x.get(0)).collect(Collectors.toList()));

        return result;
    }


    public String pcoa(String taskId, String domain, String level, String betaMethod) {

        String fileName = StrUtil.format("PCoA.{}.{}.{}.html", domain, level, betaMethod);
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File file = FileUtil.file(outputDir, "PCoA_Figure", fileName);
        return FileUtil.readUtf8String(file);
    }


    public Object treeMapData(String taskId, String group) {
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        File file = FileUtil.file(outputDir, "Treemap_Figure",
                StrUtil.format("Treemap_{}_Top14_data.csv", group));
        List<List<String>> result = readLinesToListStr(file);
        return result;
    }

    public Object lefseData(String taskId, String domain, String level) {

        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(taskId));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(taskId));

        // 获取当前任务的结果文件
        File file = FileUtil.file(outputDir, "Lefse_Table", StrUtil.format("lefse.{}.{}.res", domain, level));
        if (!file.exists()) {
            return null;
        }
        List<String> lines = FileUtil.readLines(file, CharsetUtil.CHARSET_UTF_8);

        List<String[]> lineArr = lines.stream().map(x -> x.split(StrPool.TAB)).toList();
        List<String[]> collect = lineArr.stream().filter(x -> !(x[4].equals(StrPool.DASHED)) && !(x[3].isEmpty())).sorted((x, y) -> {
            double v = Double.parseDouble(x[3]) - Double.parseDouble(y[3]);
            if (v > 0) {
                return -1;
            } else if (v == 0) {
                return 0;
            } else {
                return 1;
            }
        }).toList();
        Map<String, List<String[]>> map = new LinkedHashMap<>();
        collect.forEach(x -> {
            String group = x[2];
            if (map.containsKey(group) && map.get(group).size() < 10) {
                List<String[]> list = map.get(group);
                String[] value = {x[0], x[3]};
                list.add(value);
                map.put(group, list);
            } else if (!map.containsKey(group)) {
                ArrayList<String[]> list = new ArrayList<>();
                String[] value = {x[0], x[3]};
                list.add(value);
                map.put(group, list);
            }
        });
        JSONObject result = new JSONObject();
        LinkedHashMap<String, List<List<String>>> groupInfo = FileUtil.readLines(FileUtil.file(inputDir, "Group.txt"), CharsetUtil.CHARSET_UTF_8).stream().skip(1).map(x -> Arrays.asList(x.split(StrPool.TAB))).collect(Collectors.groupingBy(x -> x.get(1), LinkedHashMap::new, Collectors.toList()));
        result.put("groups", groupInfo.keySet());

        // 如果数据只有两组，则翻转第二组的数据
        if (map.size() == 2) {
            List<String> keys = new ArrayList<>(groupInfo.keySet());
            String secondKey = keys.get(1);
            List<String[]> strings = map.get(secondKey);
            map.put(secondKey, CollUtil.reverse(strings));
        }

        result.put("data", map);
        return result;
    }
}
