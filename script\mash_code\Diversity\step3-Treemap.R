suppressMessages({
  library(GetoptLong)
  library(tidyverse)
  library(treemapify)
})

# 定义命令行参数
GetoptLong(
  "Input=s", "Input directory for treemap tables, e.g. output/Treemap_Table",
  "Output=s", "Output directory for treemap figures, e.g. output/Treemap_Figure",
  "SelectDomain=s", "Specify domain, e.g. B",
  "SelectTaxonomy=s", "Specify taxonomy level, e.g. S",
  "Group=s", "Group table path, e.g. input/Group.txt",
  "verbose!", "Print messages"
)

# 拼接输入文件路径
infile <- file.path(Input, paste0("Treemap.", SelectDomain, ".", SelectTaxonomy, ".csv"))

if (!file.exists(infile)) stop("Input file not found: ", infile)
if (!file.exists(Group)) stop("Group file not found: ", Group)
if (!dir.exists(Output)) dir.create(Output, recursive = TRUE)

topN <- 14  # 每组选前 topN 个物种

# 自定义颜色主题（可根据需要修改）
color_theme <- c(
  "#FF6F61", "#FFB347", "#FFD966", "#A3D39C", "#70A1D7",
  "#D57A9B", "#C17FC9", "#FF9F80", "#827397", "#E8A87C",
  "#7EB6A3", "#FFB1B0", "#F5DEB3", "#92B6D5", "#BC9CFF"
)

# 读取 Bracken 丰度表
df <- read.csv(infile, check.names = FALSE)
names(df)[1] <- "taxonomy"

# 归一化为相对丰度（每个样本列除以列和）
df[,-1] <- apply(df[,-1], 2, function(x) if (sum(x) == 0) x else x / sum(x))

# 读取分组信息
group_df <- read.delim(Group, check.names = FALSE)

# 转为长格式并合并分组信息
df_long <- df %>%
  pivot_longer(cols = -taxonomy, names_to = "sample_name", values_to = "Abundance") %>%
  left_join(group_df, by = "sample_name") %>%
  filter(!is.na(group))

# 每个 group + taxonomy 的平均相对丰度
avg_abun <- df_long %>%
  group_by(group, taxonomy) %>%
  summarise(mean_abun = mean(Abundance), .groups = "drop")

# 每组选出 topN 个物种，其他归为 "Other"
data_for_plot <- avg_abun %>%
  group_by(group) %>%
  mutate(rank = rank(-mean_abun, ties.method = "first"),
         taxonomy = if_else(rank <= topN, taxonomy, "Other")) %>%
  group_by(group, taxonomy) %>%
  summarise(Abundance = sum(mean_abun), .groups = "drop") %>%
  group_by(group) %>%
  mutate(Percentage = Abundance / sum(Abundance) * 100)

# 准备颜色映射
taxa <- unique(data_for_plot$taxonomy)
taxa_no_other <- taxa[taxa != "Other"]
color_palette <- rep(color_theme, length.out = length(taxa_no_other))
colors <- c(setNames(color_palette, taxa_no_other), Other = "#B0B0B0")

# 绘图并保存每个组的 Treemap 图
for (grp in unique(data_for_plot$group)) {
  sub_data <- filter(data_for_plot, group == grp)
  # 保存每组的绘图数据为 CSV
  csv_out_file <- file.path(Output, paste0("Treemap_", grp, "_Top", topN, "_data.csv"))
  write.csv(sub_data, file = csv_out_file, row.names = FALSE)
  p <- ggplot(sub_data, aes(
    area = Percentage,
    fill = taxonomy,
    label = paste0(taxonomy, "\n", round(Percentage, 1), "%")
  )) +
    geom_treemap() +
    geom_treemap_text(
      colour = "white",
      place = "centre",
      grow = FALSE,
      reflow = TRUE,
      size = 14
    ) +
    scale_fill_manual(values = colors) +
    ggtitle(paste0("Group: ", grp)) +
    theme_void() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16),
      legend.position = "none"
    )

  out_file <- file.path(Output, paste0("Treemap_", grp, "_Top", topN, ".pdf"))
  ggsave(out_file, plot = p, width = 10, height = 8)

  if (exists("verbose") && verbose) {
    message("Saved Treemap for group ", grp, " to ", out_file)
  }
}

