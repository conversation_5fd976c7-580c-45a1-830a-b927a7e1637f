#!/usr/bin/env python3
"""
KO数据处理脚本

该脚本读取TSV文件（行为KO_ID，列为MASH_{sample_name}），以及KO_ID和sample_name的列表文件，
为每个KO_ID生成一个包含sample_name和对应值的TSV文件。

功能特性:
- 自动处理列名中的MASH_前缀（MASH_RUN001 -> RUN001）
- 自动过滤掉值为0的记录
- 跳过没有非零数据的KO_ID

使用方法:
python process_ko_data.py <tsv_file> <ko_ids_file> <run_ids_file> [output_dir]

参数:
- tsv_file: 输入的TSV文件路径（行为KO_ID，列为MASH_{sample_name}）
- ko_ids_file: 包含KO_ID列表的文本文件路径（每行一个KO_ID）
- run_ids_file: 包含sample_name列表的文本文件路径（每行一个sample_name）
- output_dir: 输出目录（可选，默认为当前目录）
"""

import argparse
import os
import sys
from pathlib import Path

import pandas as pd


def read_id_list(file_path):
    """读取ID列表文件，返回ID列表"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            ids = [line.strip() for line in f if line.strip()]
        return ids
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}: {e}")
        sys.exit(1)


def read_tsv_data(tsv_file):
    """读取TSV文件，返回DataFrame，并处理列名中的MASH_前缀"""
    try:
        df = pd.read_csv(tsv_file, sep='\t', index_col=0)

        # 处理列名，去掉MASH_前缀
        original_columns = df.columns.tolist()
        new_columns = []

        for col in original_columns:
            if col.startswith('MASH_'):
                new_col = col[5:]  # 去掉前5个字符 'MASH_'
                new_columns.append(new_col)
                print(f"列名转换: {col} -> {new_col}")
            else:
                new_columns.append(col)
                print(f"列名保持: {col}")

        df.columns = new_columns
        print(f"处理后的列名: {list(df.columns)}")

        return df
    except Exception as e:
        print(f"错误：无法读取TSV文件 {tsv_file}: {e}")
        sys.exit(1)


def process_ko_data(tsv_file, ko_ids_file, run_ids_file, output_dir='.'):
    """
    处理KO数据的主函数

    Args:
        tsv_file: TSV数据文件路径
        ko_ids_file: KO_ID列表文件路径
        run_ids_file: sample_name列表文件路径
        output_dir: 输出目录路径
    """

    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 读取数据
    print("正在读取数据文件...")
    ko_ids = read_id_list(ko_ids_file)
    run_ids = read_id_list(run_ids_file)
    data_df = read_tsv_data(tsv_file)

    print(f"读取到 {len(ko_ids)} 个KO_ID")
    print(f"读取到 {len(run_ids)} 个sample_name")
    print(f"TSV数据维度: {data_df.shape}")

    # 检查数据完整性
    missing_ko_ids = [ko_id for ko_id in ko_ids if ko_id not in data_df.index]
    missing_run_ids = [run_id for run_id in run_ids if run_id not in data_df.columns]

    if missing_ko_ids:
        print(f"警告：以下KO_ID在TSV文件中未找到: {missing_ko_ids[:10]}{'...' if len(missing_ko_ids) > 10 else ''}")

    if missing_run_ids:
        print(
            f"警告：以下sample_name在TSV文件中未找到: {missing_run_ids[:10]}{'...' if len(missing_run_ids) > 10 else ''}")

    # 为每个KO_ID生成结果文件
    print("正在生成结果文件...")
    processed_count = 0

    for ko_id in ko_ids:
        if ko_id not in data_df.index:
            print(f"跳过KO_ID {ko_id}（在TSV文件中未找到）")
            continue

        # 提取该KO_ID对应的数据
        ko_data = data_df.loc[ko_id]

        # 创建结果DataFrame，过滤掉值为0的记录
        result_data = []
        for run_id in run_ids:
            if run_id in ko_data.index:
                value = ko_data[run_id]
                # 过滤掉值为0的记录
                if pd.notna(value) and value != 0:
                    result_data.append([run_id, value])
            else:
                # 如果sample_name不存在，跳过（不添加到结果中）
                print(f"警告：sample_name {run_id} 在KO_ID {ko_id} 的数据中未找到")

        # 如果没有非零数据，跳过该KO_ID
        if not result_data:
            print(f"跳过KO_ID {ko_id}（没有非零数据）")
            continue

        # 创建结果DataFrame
        result_df = pd.DataFrame(result_data, columns=['sample_name', 'Value'])

        # 保存结果文件
        output_file = output_path / f"{ko_id}.tsv"
        result_df.to_csv(output_file, sep='\t', index=False)

        processed_count += 1
        if processed_count % 100 == 0:
            print(f"已处理 {processed_count} 个KO_ID...")

    print(f"处理完成！共生成 {processed_count} 个结果文件")
    print(f"输出目录: {output_path.absolute()}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="处理KO数据，为每个KO_ID生成包含sample_name和对应值的TSV文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python process_ko_data.py data.tsv ko_ids.txt run_ids.txt
  python process_ko_data.py data.tsv ko_ids.txt run_ids.txt output/
        """
    )

    parser.add_argument('tsv_file', help='输入的TSV文件路径（行为KO_ID，列为sample_name）')
    parser.add_argument('ko_ids_file', help='包含KO_ID列表的文本文件路径')
    parser.add_argument('run_ids_file', help='包含sample_name列表的文本文件路径')
    parser.add_argument('output_dir', help='输出目录（默认为当前目录）')

    args = parser.parse_args()

    # 检查输入文件是否存在
    for file_path in [args.tsv_file, args.ko_ids_file, args.run_ids_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件不存在: {file_path}")
            sys.exit(1)

    # 执行处理
    process_ko_data(args.tsv_file, args.ko_ids_file, args.run_ids_file, args.output_dir)


if __name__ == "__main__":
    main()
    # process_ko_data(rf'D:\data\demo\All.KO_Sample.wide.RA.tsv', rf'D:\data\demo\KO_list.txt',
    #                 rf'D:\data\demo\sample_name_LIST.txt', rf'./')
