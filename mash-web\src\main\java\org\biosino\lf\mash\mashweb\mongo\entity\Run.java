package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

@Data
@Document("mash_runs")
public class Run implements Serializable {
    @Id
    private String id;

    @Field("run_id")
    private String runId;

    @Field("project_id")
    private String projectId;

    @Field("sample_id")
    private String sampleId;

    @Field("habitat_source")
    private String habitatSource;

    @Field("env_zone")
    private String envZone;

    @Field("env_medium")
    private String envMedium;

    @Field("fractionation")
    private String fractionation;

    private String instrument;

    @Field("geo_loc_name")
    private String geoLocName;

    private Double depth;

    private Double latitude;

    private Double longitude;

    private Double temperature;

    @Field("center_name")
    private String centerName;

    @Field("seq_num_contigs")
    private String seqNumAssembled;

    @Field("seq_base_contigs_bp")
    private String seqBaseAssembledBp;

    @Field("seq_num_reads")
    private String seqNumReads;

    @Field("seq_base_reads1_bp")
    private String seqBaseReads1Bp;

    @Field("seq_base_reads2_bp")
    private String seqBaseReads2Bp;

    @Field("oxygen_sensor")
    private Double oxygenSensor;

    @Field("chlorophyll_sensor")
    private Double chlorophyllSensor;

    @Field("nitrate_sensor")
    private Double nitrateSensor;

    @Field("salinity_sensor")
    private Double salinitySensor;

    private Double ph;

    @Field("project_name")
    private String projectName;

    private String study;

    @Field("project_abstract")
    private String projectAbstract;
}
