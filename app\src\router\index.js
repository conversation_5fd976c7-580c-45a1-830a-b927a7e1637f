import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import _ from 'lodash';

const router_info = [
  {
    path: '',
    redirect: '/home',
  },
  {
    path: '/home',
    name: 'Home',
    headTitle: 'Home',
    component: () => import('@/views/home/<USER>'),
    props: route => ({
      targetSlide: Number(route.query.slide) || 0,
    }),
  },
  {
    path: '/browse',
    name: 'browse',
    headTitle: 'Data Browsing',
    component: () => import('@/views/browse/index.vue'),
  },

  {
    path: '/diversity',
    name: 'diversity',
    headTitle: 'Diversity Explorer',
    component: () => import('@/views/diversity/index.vue'),
  },
  {
    path: '/sample/detail/:id',
    name: 'sampleDetail',
    headTitle: 'Sample Detail',
    component: () => import('@/views/browse/detail.vue'),
  },
  {
    path: '/genomic',
    name: 'genomic',
    headTitle: 'genomic Explorer',
    component: () => import('@/views/genomic/index.vue'),
  },
  {
    path: '/scholarly',
    name: 'scholarly',
    headTitle: 'Scholarly Archive',
    component: () => import('@/views/scholarly/index.vue'),
  },
  {
    path: '/esIndex',
    name: 'EsIndex',
    component: () => import('@/views/esIndex.vue'),
  },
  {
    path: '/expert-qa',
    name: 'expert-qa',
    headTitle: 'Expert Q&A',
    component: () => import('@/views/expert-qa/index.vue'),
  },
  {
    path: '/visits-log',
    name: 'VisitsLog',
    component: () => import('@/views/visits-log/index.vue'),
  },
];

export const router_info_export = _.clone(router_info);

NProgress.configure({ showSpinner: false });
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: router_info,
});

router.beforeEach((to, from, next) => {
  NProgress.start();
  // 在每次导航之前滚动到页面顶部
  window.scrollTo({
    top: 0,
  });
  next();
  NProgress.done();
});

export default router;
