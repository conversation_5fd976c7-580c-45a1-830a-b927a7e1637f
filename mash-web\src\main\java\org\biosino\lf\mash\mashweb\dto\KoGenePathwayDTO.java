package org.biosino.lf.mash.mashweb.dto;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@Data
public class KoGenePathwayDTO {

    @Alias("KO")
    private String ko;

    @Alias("Genes")
    private String genes;

    @Alias("PathwayIDs")
    private String pathwayIds;

    @Alias("mean_abundance")
    private String meanAbundance;

    @Alias("Type")
    private String type;

    private String vsTitle;

    private String pValue;
}
