#!/usr/bin/env python3
"""
物种数据处理脚本

该脚本读取CSV文件（行为species_name，列为run_id），以及species_name和run_id的列表文件，
为每个species_name生成一个包含run_id和对应值的TSV文件。

功能特性:
- 自动过滤掉值为0的记录
- 跳过没有非零数据的species_name

使用方法:
python process_species_data.py <csv_file> <species_names_file> <run_ids_file> [output_dir]

参数:
- csv_file: 输入的CSV文件路径（行为species_name，列为run_id）
- species_names_file: 包含species_name列表的文本文件路径（每行一个species_name）
- run_ids_file: 包含run_id列表的文本文件路径（每行一个run_id）
- output_dir: 输出目录（可选，默认为当前目录）
"""

import argparse
import os
import sys
from pathlib import Path

import pandas as pd


def read_id_list(file_path):
    """读取ID列表文件，返回ID列表"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            ids = [line.strip() for line in f if line.strip()]
        return ids
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}: {e}")
        sys.exit(1)


def read_csv_data(csv_file):
    """读取CSV文件，返回DataFrame"""
    try:
        df = pd.read_csv(csv_file, index_col=0)
        print(f"处理后的列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"错误：无法读取CSV文件 {csv_file}: {e}")
        sys.exit(1)


def process_species_data(csv_file, species_names_file, run_ids_file, output_dir='.'):
    """
    处理物种数据的主函数

    Args:
        csv_file: CSV数据文件路径
        species_names_file: species_name列表文件路径
        run_ids_file: run_id列表文件路径
        output_dir: 输出目录路径
    """

    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 读取数据
    print("正在读取数据文件...")
    species_names = read_id_list(species_names_file)
    run_ids = read_id_list(run_ids_file)
    data_df = read_csv_data(csv_file)

    print(f"读取到 {len(species_names)} 个species_name")
    print(f"读取到 {len(run_ids)} 个run_id")
    print(f"CSV数据维度: {data_df.shape}")

    # 检查数据完整性
    missing_species_names = [species_name for species_name in species_names if species_name not in data_df.index]
    missing_run_ids = [run_id for run_id in run_ids if run_id not in data_df.columns]

    if missing_species_names:
        print(
            f"警告：以下species_name在CSV文件中未找到: {missing_species_names[:10]}{'...' if len(missing_species_names) > 10 else ''}")

    if missing_run_ids:
        print(f"警告：以下run_id在CSV文件中未找到: {missing_run_ids[:10]}{'...' if len(missing_run_ids) > 10 else ''}")

    # 为每个species_name生成结果文件
    print("正在生成结果文件...")
    processed_count = 0

    for species_name in species_names:
        if species_name not in data_df.index:
            print(f"跳过species_name {species_name}（在CSV文件中未找到）")
            continue

        # 提取该species_name对应的数据
        species_data = data_df.loc[species_name]

        # 创建结果DataFrame，过滤掉值为0的记录
        result_data = []
        for run_id in run_ids:
            if run_id in species_data.index:
                value = species_data[run_id]
                # 过滤掉值为0的记录
                if pd.notna(value) and value != 0:
                    result_data.append([run_id, value])
            else:
                # 如果run_id不存在，跳过（不添加到结果中）
                print(f"警告：run_id {run_id} 在species_name {species_name} 的数据中未找到")

        # 如果没有非零数据，跳过该species_name
        if not result_data:
            print(f"跳过species_name {species_name}（没有非零数据）")
            continue

        # 创建结果DataFrame
        result_df = pd.DataFrame(result_data, columns=['sample_name', 'Value'])

        # 保存结果文件
        output_file = output_path / f"{species_name}.tsv"
        result_df.to_csv(output_file, sep='\t', index=False)

        processed_count += 1
        if processed_count % 100 == 0:
            print(f"已处理 {processed_count} 个species_name...")

    print(f"处理完成！共生成 {processed_count} 个结果文件")
    print(f"输出目录: {output_path.absolute()}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="处理物种数据，为每个species_name生成包含run_id和对应值的TSV文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python process_species_data.py data.csv species_names.txt run_ids.txt
  python process_species_data.py data.csv species_names.txt run_ids.txt output/
        """
    )

    parser.add_argument('csv_file', help='输入的CSV文件路径（行为species_name，列为run_id）')
    parser.add_argument('species_names_file', help='包含species_name列表的文本文件路径')
    parser.add_argument('run_ids_file', help='包含run_id列表的文本文件路径')
    parser.add_argument('output_dir', nargs='?', default='.',
                        help='输出目录（默认为当前目录）')

    args = parser.parse_args()

    # 检查输入文件是否存在
    for file_path in [args.csv_file, args.species_names_file, args.run_ids_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件不存在: {file_path}")
            sys.exit(1)

    # 执行处理
    process_species_data(args.csv_file, args.species_names_file, args.run_ids_file, args.output_dir)


if __name__ == "__main__":
    main()
