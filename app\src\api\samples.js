import request from '@/utils/request';

const baseURL = '/samples';

// 登录方法
export function list(params) {
  return request({
    url: `${baseURL}/list`,
    method: 'post',
    data: params,
  });
}

export function searchSelectWord(data) {
  return request({
    url: `${baseURL}/searchSelectWord`,
    method: 'post',
    data: data,
  });
}

export function getMapLocation(params) {
  return request({
    url: `${baseURL}/getMapLocation`,
    method: 'post',
    data: params,
  });
}

export function getNodeDataByExperimentIds(params) {
  return request({
    url: `${baseURL}/getNodeDataByExperimentIds`,
    method: 'post',
    data: params,
  });
}

// 获取NODE数据的可访问状态
export function getNodeDataAccessStatus(ids) {
  return request({
    url: `${baseURL}/getNodeDataAccessStatus`,
    method: 'post',
    data: ids,
  });
}

// 申请NODE数据
export function applyNodeData(params) {
  return request({
    url: `${baseURL}/applyNodeData`,
    method: 'post',
    data: params,
  });
}

// 获取按国家分布的样本统计数据
export function getChartCountryDistribution(query) {
  return request({
    url: `${baseURL}/getChartCountryDistribution`,
    method: 'post',
    data: query,
  });
}

// 获取按水体类型分布的样本统计数据
export function getChartWaterBodyTypeDistribution(params) {
  return request({
    url: `${baseURL}/getChartWaterBodyTypeDistribution`,
    method: 'post',
    data: params,
  });
}

// 获取按组学类型分布的样本统计数据
export function getChartOmicsTypeDistribution(query) {
  return request({
    url: `${baseURL}/getChartOmicsTypeDistribution`,
    method: 'post',
    data: query,
  });
}

// 获取按技术类型分布的样本统计数据
export function getChartTechnologyTypeDistribution(params) {
  return request({
    url: `${baseURL}/getChartTechnologyTypeDistribution`,
    method: 'post',
    data: params,
  });
}

// 获取按水体名称分布的样本统计数据
export function getWaterBodyNameDistribution(params) {
  return request({
    url: `${baseURL}/getWaterBodyNameDistribution`,
    method: 'post',
    data: params,
  });
}

// 申请Ant Nest数据
export function applyAntNestData(params) {
  return request({
    url: `${baseURL}/applyAntNestData`,
    method: 'post',
    data: params,
  });
}

// 获取AntNest管理员邮箱
export function getAntNestAdminEmail() {
  return request({
    url: `${baseURL}/getAntNestAdminEmail`,
    method: 'get',
  });
}

// 获取首页地图点位数据
export function getHomeMapLocation(params) {
  return request({
    url: `${baseURL}/getHomeMapLocation`,
    method: 'post',
    data: params,
  });
}

// 获取根据水文圈类型过滤的水体类型
export function getWaterBodyTypeByHydrosphere(params) {
  return request({
    url: `${baseURL}/getWaterBodyTypeByHydrosphere`,
    method: 'get',
    params: params,
  });
}

// 根据RunId获取样本详情
export function getSampleByRunId(runId) {
  return request({
    url: `${baseURL}/getRunInfoByRunId`,
    method: 'get',
    params: { runId },
  });
}

// 获取KO Pathway详情数据
export function getKoPathwayDetail(runId) {
  return request({
    url: `${baseURL}/getKoPathwayDetail`,
    method: 'get',
    params: { runId },
  });
}

export function existKrona(runId) {
  return request({
    url: `${baseURL}/existKrona`,
    method: 'get',
    params: { runId },
  });
}
