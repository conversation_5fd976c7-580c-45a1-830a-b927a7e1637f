###
#' @Date: 2023-06-14 16:48:43
#' @LastEditors: LiuyangLi
#' @LastEditTime: 2023-08-16 11:29:57
#' @FilePath: /liliuyang/R/Rscript.MASH/202308/M1S2.ko.depth.merge.cmd.R
#' @Description:
###

suppressMessages(library(GetoptLong))
#setwd('/data1/liliuyang/photohydro/20230309/data/AAA_Rplot')
rm(list = ls(all.names = TRUE))
packages=c("reshape2","dplyr")
ipak <- function(pkg){
    new.pkg <- pkg[!(pkg %in% installed.packages()[, "Package"])]
    if (length(new.pkg))
        install.packages(new.pkg, dependencies = TRUE, repos="http://mirrors.tuna.tsinghua.edu.cn/CRAN/")
    sapply(pkg, require, character.only = TRUE)
    cat(paste0("成功载入包: ", pkg, "\n"))
}
ipak(packages)
GetoptLong(
  "in_KO_Sample_long=s", "KO_depth_summary.tsv",
  "out_KO_Sample_wide=s", "output",
  "verbose!","print messages"
)

# '/data1/liliuyang/MASH-OCEAN/20230613/data/AAA_KO_depth_summary/KO_depth_summary.tsv'
KO_Sample = in_KO_Sample_long  %>%
  {read.csv(., sep = '\t', quote='', header = F, col.names = c('KO','Avg_fold'))} %>%
  {mutate(.,
         Sample = sapply(as.character(.$KO),
                         function(x) unlist(strsplit(x, "---"))[1]),
         KO = sapply(as.character(.$KO),
                     function(x) unlist(strsplit(x, "---"))[2])
         )} %>%
  {reshape2::dcast(.,
                   formula = formula("KO ~ Sample"),
                   value.var = "Avg_fold",
                   fun.aggregate = sum,
                   fill = 0)} %>%
  {write.table(., out_KO_Sample_wide, row.names = FALSE, col.names = T,
               sep = '\t', quote = FALSE, fileEncoding="UTF-8")}


print("M1S2.ko.depth.merge.cmd.R finished!")
