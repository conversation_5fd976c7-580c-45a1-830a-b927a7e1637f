package org.biosino.lf.mash.mashweb;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSON;
import lombok.Data;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.enums.ExpertQaTypeEnum;
import org.biosino.lf.mash.mashweb.mongo.entity.ExpertInfo;
import org.biosino.lf.mash.mashweb.mongo.entity.MashArticleMongo;
import org.biosino.lf.mash.mashweb.mongo.entity.Run;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.ExpertInfoRepository;
import org.biosino.lf.mash.mashweb.mongo.repository.RunRepository;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesRepository;
import org.biosino.lf.mash.mashweb.mongo.repository.SearchLogRepository;
import org.biosino.lf.mash.mashweb.service.ScholarlyArchiveService;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@SpringBootTest
public class MongoTest {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private ScholarlyArchiveService scholarlyArchiveService;
    @Autowired
    private SearchLogRepository searchLogRepository;
    @Autowired
    private ExpertInfoRepository expertInfoRepository;
    @Autowired
    private RunRepository runRepository;
    @Autowired
    private SamplesRepository samplesRepository;

    /**
     * pmid去重
     */
    @Test
    public void mergePimd() {
        final Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.group("pmid").count().as("count"),
                Aggregation.match(Criteria.where("count").gt(1)),
                Aggregation.project("pmid")
        );

        final Class<MashArticleMongo> clz = MashArticleMongo.class;
        final AggregationResults<Document> results = mongoTemplate.aggregate(
                aggregation,
                clz,
                Document.class
        );

        final List<Document> docs = results.getMappedResults();
        // 获取所有重复的pmid值
        final List<Long> pmids = docs
                .stream()
                .map(x -> getLongVal(x, "_id"))
                .toList();
        if (CollUtil.isEmpty(pmids)) {
            System.out.println("不存在重复的pmid");
            return;
        }
        System.out.println(pmids);
        final List<MashArticleMongo> list = mongoTemplate.find(Query.query(Criteria.where("pmid").in(pmids)), clz);
        /*Map<Long, List<MashArticleMongo>> map = new HashMap<>();
        for (MashArticleMongo mashArticleMongo : list) {
            final Long pmid = mashArticleMongo.getPmid();
            List<MashArticleMongo> mashArticleMongos = map.get(pmid);
            if (mashArticleMongos == null) {
                mashArticleMongos = new ArrayList<>();
            }
            mashArticleMongos.add(mashArticleMongo);
            map.put(pmid, mashArticleMongos);
        }*/
        final Map<Long, List<MashArticleMongo>> map = list.stream()
                .collect(Collectors.groupingBy(MashArticleMongo::getPmid));

        final Map<Long, List<IdAndContentStatus>> pmidAndContentStatus = new HashMap<>();
        for (Map.Entry<Long, List<MashArticleMongo>> entry : map.entrySet()) {
            Set<MashArticleMongo> set = new HashSet<>();
            List<MashArticleMongo> vals = entry.getValue();
            for (MashArticleMongo articleMongo : vals) {
                final MashArticleMongo.PdfContent pdfDocContent = articleMongo.getPdfDocContent();
                final List<String> ncbiSraIds = articleMongo.getNcbiSraIds();
                final List<String> nodeIds = articleMongo.getNodeIds();


                final IdAndContentStatus status = new IdAndContentStatus();
                status.setId(articleMongo.getId());
                if (CollUtil.isNotEmpty(nodeIds) || CollUtil.isNotEmpty(ncbiSraIds) || pdfDocContent != null) {
                    status.setHasContent(true);
                } else {
                    status.setHasContent(false);
                }
                final Long pmid = articleMongo.getPmid();
                List<IdAndContentStatus> list2 = pmidAndContentStatus.get(pmid);
                if (list2 == null) {
                    list2 = new ArrayList<>();
                }
                list2.add(status);
                pmidAndContentStatus.put(pmid, list2);

                articleMongo.setId(null);
                articleMongo.setPdfDocContent(null);
                articleMongo.setNcbiSraIds(null);
                articleMongo.setNodeIds(null);
                set.add(articleMongo);
            }
            if (set.size() != 1) {
                System.out.println("重复的pmid：" + entry.getKey());
            }
        }


        for (Map.Entry<Long, List<IdAndContentStatus>> entry : pmidAndContentStatus.entrySet()) {
            final List<IdAndContentStatus> values = entry.getValue();
            String keepId = null;
            final List<String> delIds = new ArrayList<>();
            for (final IdAndContentStatus value : values) {
                final String id = value.getId();
                if (keepId == null && value.isHasContent()) {
                    keepId = id;
                } else {
                    delIds.add(id);
                }
            }
            if (keepId == null) {
                keepId = delIds.remove(0);
            }

            final Long pmid = entry.getKey();
            for (String delId : delIds) {
                System.out.println(StrUtil.format("pmid: {},保留id:{},待删除id：{}", pmid, keepId, delId));
                mongoTemplate.remove(Query.query(Criteria.where("_id").is(new ObjectId(delId))), clz);
            }
        }


    }

    @Data
    public static class IdAndContentStatus {
        private String id;
        private boolean hasContent;
    }


    private Long getLongVal(Document doc, String field) {
        long val;
        try {
            val = doc.getLong(field);
        } catch (Exception e) {
            Integer intVal = doc.getInteger(field);
            val = intVal != null ? intVal.longValue() : 0;
        }
        return val;
    }

    /**
     * 添加创建时间（测试用）
     */
    @Test
    public void addCreateDate() {
        final Date date = DateUtil.parseDate("2025-03-01");
        long time = date.getTime();
//        final Map<String, Long> map = new ConcurrentHashMap<>();
//        map.put("start", time);

        final Class<MashArticleMongo> clz = MashArticleMongo.class;
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            final PageRequest page = PageRequest.of(i, 1000, Sort.by(Sort.Direction.ASC, "_id"));
            final Query query = new Query(Criteria.where("createDate").exists(false));
            query.with(page);

            List<MashArticleMongo> list = mongoTemplate.find(query, clz);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            System.out.println(StrUtil.format("正在处理第{}页", i + 1));
            final List<Long> pmids = new ArrayList<>();
            for (MashArticleMongo item : list) {
                pmids.add(item.getPmid());
            }

            final Query updateQuery = new Query(Criteria.where("pmid").in(pmids));
            final Update update = new Update().set("createDate", new Date(time += (1000 * 600)));
            mongoTemplate.updateMulti(updateQuery, update, clz);
        }
    }

    /**
     * 删除samples表中不存在的node_ids和ncbi_sra_ids
     */
    @Test
    public void removeBioIds() {
        Query query = new Query().cursorBatchSize(500).noCursorTimeout();
        // mongoTemplate.stream(query, MashArticleMongo.class);

        Set<String> bioIds = new HashSet<>();
        try (Stream<Samples> stream = mongoTemplate.stream(query, Samples.class)) {
            stream.map(x -> {
                if (x.getBioProjectId() != null) {
                    bioIds.add(x.getBioProjectId());
                }
                if (x.getRunId() != null) {
                    bioIds.add(x.getRunId());
                }
                if (x.getBioSampleId() != null) {
                    bioIds.add(x.getBioSampleId());
                }
                if (x.getExperimentId() != null) {
                    bioIds.add(x.getExperimentId());
                }
                return x.getRunId();
            }).count();

        }

        query = new Query(new Criteria().orOperator(Criteria.where("node_ids").exists(true)
                , Criteria.where("ncbi_sra_ids").exists(true))).cursorBatchSize(500).noCursorTimeout();
        try (Stream<MashArticleMongo> stream = mongoTemplate.stream(query, MashArticleMongo.class)) {
            stream.map(x -> {
                List<String> ids = x.getNodeIds();
                boolean changed = false;
                if (ids != null) {
                    Iterator<String> iterator = ids.iterator();
                    // 保留所有sample中存在的node id
                    final Set<String> existIdInSample = new TreeSet<>();
                    while (iterator.hasNext()) {
                        final String next = iterator.next();
                        /*if ("OEP003832".equals(next)) {
                            System.out.println(next);
                        }*/
                        int length = next.length();
                        String otherNodeId;
                        if (length <= 3 + 6) {
                            // 转8位node id
                            String pre = next.substring(0, 3);
                            String num = next.substring(3, length);
                            otherNodeId = pre + StrUtil.padPre(num, 8, "0");
                        } else {
                            // 转6位node id
                            String pre = next.substring(0, 3);
                            String num = next.substring(3 + 2, length);
                            otherNodeId = pre + num;
                        }

                        if (bioIds.contains(next)) {
                            existIdInSample.add(next);
                        }
                        if (bioIds.contains(otherNodeId)) {
                            existIdInSample.add(otherNodeId);
                        }
                    }
                    if (CollUtil.isEmpty(existIdInSample)) {
                        x.setNodeIds(null);
                    } else {
                        x.setNodeIds(new ArrayList<>(existIdInSample));
                    }
                    // 包含node id都要更新
                    changed = true;
                }

                ids = x.getNcbiSraIds();
                if (ids != null) {
                    Iterator<String> iterator = ids.iterator();
                    while (iterator.hasNext()) {
                        String next = iterator.next();
                        if (!bioIds.contains(next)) {
                            iterator.remove();
                            changed = true;
                        }
                    }
                    x.setNcbiSraIds(CollUtil.isEmpty(ids) ? null : ids);
                }

                if (changed) {
                    System.out.println(StrUtil.format("更新pmid: {}", x.getPmid()));
                    mongoTemplate.save(x);
                }
                return x.getPmid();
            }).count();
        }
    }


    /**
     * 更新热词
     */
    @Test
    public void updateHotSpot() {
        final ArrayList<String> list = CollUtil.toList(
                "Mariana Trench Environment and Ecology（MEER）",
                "Carbon, Nitrogen, and Sulfur Cycles",
                "Microbial Interactions",
                "Dimethylsulfoniopropionate (DMSP) Cycling",
                "Microbial Energy Utilization",
                "Microbial Cultivation"
        );
        searchLogRepository.deleteAll();
        int maxNum = CollUtil.size(list) + 10;
        for (String searchKeyword : list) {
            scholarlyArchiveService.saveSearchLog(searchKeyword, maxNum--);
        }
    }

    /**
     * 生成文件，用于入库到Milvus向量库
     */
    @Test
    public void generateMashMilvus() {
        final Class<MashArticleMongo> clz = MashArticleMongo.class;
        final File dir = new File("D:\\Temp\\mash_data\\milvus\\data");
        if (!dir.exists()) {
            dir.mkdirs();
        }
        final File txtDir = new File(dir, "mash_txt");
        if (!txtDir.exists()) {
            txtDir.mkdirs();
        }

        int fileCount = 0;
        final ArrayList<Map<String, Object>> rows = new ArrayList<>();
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            final PageRequest page = PageRequest.of(i, 1000, Sort.by(Sort.Direction.ASC, "_id"));
            final Query query = new Query(Criteria.where("pdf_doc_content.body").exists(true));
            query.with(page);

            List<MashArticleMongo> list = mongoTemplate.find(query, clz);
            if (CollUtil.isEmpty(list)) {
                break;
            }

            for (MashArticleMongo articleMongo : list) {
                final Long pmid = articleMongo.getPmid();
                final File file = new File(txtDir, pmid + ".txt");
                if (!file.exists()) {
                    final MashArticleMongo.PdfContent pdfDocContent = articleMongo.getPdfDocContent();
                    final List<String> body = pdfDocContent.getBody();
                    FileUtil.writeUtf8Lines(body, file);
                }
                fileCount++;

                final Map<String, Object> row = new LinkedHashMap<>();
                row.put("PMID", pmid.toString());
                row.put("Title", articleMongo.getTitle());
                row.put("DOI", articleMongo.getDoi());
                row.put("published_year", articleMongo.getYear());
                row.put("journal_title", articleMongo.getJournalTitle());

                // 获取最新年份的影响因子
                row.put("impact_factor", getLastImpactFactor(articleMongo.getYearIfs()));

                final List<MashArticleMongo.ZkySections> zkySections = articleMongo.getZkySections();
                row.put("section_names_cn", CollUtil.isEmpty(zkySections) ? null :
                        (CollUtil.join(zkySections.stream().map(MashArticleMongo.ZkySections::getNameCn).collect(Collectors.toList()), "|"))
                );
                rows.add(row);
            }

        }
        System.out.println("文件数量：" + fileCount);

        final File excelFile = new File(dir, "mash_metadata.xlsx");
        if (excelFile.exists()) {
            excelFile.delete();
        }
        // 通过工具类创建writer
        try (final BigExcelWriter writer = ExcelUtil.getBigWriter(excelFile)) {
            // 一次性写出内容，使用默认样式，强制输出标题
            writer.write(rows, true);
        }
        System.out.println("生成元数据excel：" + excelFile.getAbsolutePath());
    }

    /**
     * 获取最新年份的影响因子
     */
    private Double getLastImpactFactor(final List<MashArticleMongo.ImpactFactor> yearIfs) {
        Double d = null;
        if (CollUtil.isNotEmpty(yearIfs)) {
            // 初始化最大值变量
            int maxYear = Integer.MIN_VALUE;
            double maxImpactFactor = 0.0;
            for (MashArticleMongo.ImpactFactor item : yearIfs) {
                Integer year = item.getYear();
                // 更新最大值
                if (year > maxYear) {
                    maxYear = year;
                    maxImpactFactor = item.getImpactFactor();
                }
            }
            d = maxImpactFactor;
        }
        return d;
    }


    /**
     * 保存专家信息
     */
    @Test
    public void saveExperts() throws IOException {
        try (InputStream stream = this.getClass().getResourceAsStream("/json/expert.json")) {
            expertInfoRepository.deleteAll();
            final String json = IoUtil.readUtf8(stream);
            final List<ExpertInfo> expertInfo = JSON.parseArray(json, ExpertInfo.class);
            if (CollUtil.isNotEmpty(expertInfo)) {
                final List<String> allLabels = ExpertQaTypeEnum.allLabels();
                for (ExpertInfo item : expertInfo) {
                    final String name = item.getName();
                    final String email = item.getEmail();
                    if (!ReUtil.isMatch(RegexPool.EMAIL, email)) {
                        throw new ServiceException(StrUtil.format("专家邮箱错误：{}", name));
                    }
                    final List<String> labels = item.getLabels();
                    if (CollUtil.isNotEmpty(labels)) {
                        for (String label : labels) {
                            if (!allLabels.contains(label)) {
                                throw new ServiceException(StrUtil.format("专家 {} ，包含未知的专家标签：{}", name, label));
//                                System.out.println(StrUtil.format("专家 {} ，包含未知的专家标签：{}", name, label));
                            }
                        }
                    } else {
                        throw new ServiceException(StrUtil.format("专家 {} ，未指定专家标签", name));
                    }

                  /*  final List<String> type = item.getType();
                    if (CollUtil.isEmpty(type)) {
                        throw new ServiceException(StrUtil.format("专家 {} ，未指定专家类型", name));
                    } else {

                    }*/
                }
                expertInfoRepository.saveAll(expertInfo);
                System.out.println(StrUtil.format("保存专家信息成功，共:{}条", CollUtil.size(expertInfo)));

                // 开发阶段不使用真实邮箱
//                mongoTemplate.updateMulti(new Query(), new Update().set("email", "<EMAIL>"), ExpertInfo.class);
            }
        }
    }

    /**
     * 处理数据，samples 将 有结果文件 的 hasResult 设置为Yes
     */
    @Test
    public void processSamplesHasResult() {
        List<String> runIds = mongoTemplate.findDistinct(new Query(), "run_id", Run.class, String.class);
        // 写一个update语句，将Samples run_id 在 runIds 里面 的has_result 字段设置为Yes
        Query query = new Query(Criteria.where("run_id").in(runIds));

        // 构造更新操作：设置 has_result 字段为 "Yes"
        Update update = new Update().set("has_result", "Yes");

        // 执行批量更新
        mongoTemplate.updateMulti(query, update, Samples.class);
    }
}
