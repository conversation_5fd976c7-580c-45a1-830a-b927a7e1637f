#! /bin/bash
scripts_path=$1
inputPath=$2
outputPath=$3
Domain=$4
Level=$5

#获取用户筛选样本的物种丰度文件
python3 ${scripts_path}/Diversity/step1-kraken2_extract_each_tax_BEAV_cmd.py ${inputPath}/bracken_16_species ${outputPath}

#准备绘图的输入文件
Rscript ${scripts_path}/Diversity/step2-kraken.Calculate.cmd.R --Source ${outputPath}/kraken2_taxonomic_profiles.tsv --Group ${inputPath}/Group.txt --Abundance ${outputPath}/Abundance_Table --Barplot ${outputPath}/Barplot_Table --Heatmap ${outputPath}/Heatmap_Table --PCoA ${outputPath}/PCoA_Table  --Treemap ${outputPath}/Treemap_Table  --SelectDomain ${Domain} --SelectTaxonomy ${Level}

#Treemap绘制
Rscript ${scripts_path}/Diversity/step3-Treemap.R --Input ${outputPath}/Treemap_Table --Output ${outputPath}/Treemap_Figure --SelectDomain ${Domain} --SelectTaxonomy ${Level} --Group ${inputPath}/Group.txt

#Barplot绘制
Rscript ${scripts_path}/Diversity/step4-kraken.Barplot.cmd.R --Input ${outputPath}/Barplot_Table --Output ${outputPath}/Barplot_Figure --SelectDomain ${Domain}  --SelectTaxonomy ${Level}  --Group ${inputPath}/Group.txt

#PCOA绘制
Rscript ${scripts_path}/Diversity/step5-kraken.PCoA.cmd.R --Group ${inputPath}/Group.txt --Group_color ${inputPath}/group_color.tsv --Input ${outputPath}/PCoA_Table --Output  ${outputPath}/PCoA_Figure --SelectDomain  ${Domain}  --SelectTaxonomy ${Level}








