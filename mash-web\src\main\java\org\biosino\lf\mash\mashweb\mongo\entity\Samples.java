package org.biosino.lf.mash.mashweb.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document(collection = "samples")
public class Samples {

    @Id
    private String id;

    @Field("run_id")
    private String runId;

    @Field("run_status")
    private String runStatus;

    @Field("bio_project_id")
    private String bioProjectId;

    @Field("bio_project_name")
    private String bioProjectName;

    @Field("bio_project_title")
    private String bioProjectTitle;

    @Field("bio_project_description")
    private String bioProjectDescription;

    @Field("bio_project_status")
    private String bioProjectStatus;

    @Field("bio_sample_id")
    private String bioSampleId;

    @Field("sample_title")
    private String sampleTitle;

    @Field("sample_description")
    private String sampleDescription;

    @Field("bio_sample_status")
    private String bioSampleStatus;

    @Field("data_source")
    private String dataSource;

    @Field("organism")
    private String organism;

    @Field("omics_type")
    private String omicsType;

    @Field("technology_type")
    private String technologyType;

    @Field("experiment_id")
    private String experimentId;

    @Field("experiment_status")
    private String experimentStatus;

    @Field("lat_lon")
    private String latLon;

    @Field("latitude")
    private Double latitude;

    @Field("longitude")
    private Double longitude;

    @Field("geo_loc_name")
    private String geoLocName;

    @Field("temperature")
    private String temperature;

    @Field("temperature_start")
    private Double temperatureStart;

    @Field("temperature_end")
    private Double temperatureEnd;

    @Field("salinity")
    private String salinity;

    @Field("salinity_start")
    private Double salinityStart;

    @Field("salinity_end")
    private Double salinityEnd;

    @Field("depth")
    private String depth;

    @Field("depth_start")
    private Double depthStart;

    @Field("depth_end")
    private Double depthEnd;

    @Field("pressure")
    private String pressure;

    @Field("pressure_start")
    private Double pressureStart;

    @Field("pressure_end")
    private Double pressureEnd;

    @Field("ph")
    private String ph;

    @Field("ph_start")
    private Double phStart;

    @Field("ph_end")
    private Double phEnd;

    @Field("biome")
    private String biome;

    @Field("biome_tag")
    private String biomeTag;

    @Field("project_tag")
    private String projectTag;

    @Field("mash_lake")
    private String mashLake;

    @Field("mash_ocean_v2")
    private String mashOceanV2;

    @Field("tara_ocean")
    private String taraOcean;

    @Field("mash_china_sea")
    private String mashChinaSea;

    @Field("meer")
    private String meer;

    @Field("sampling_substrate")
    private String samplingSubstrate;

    @Field("critical_zone")
    private String criticalZone;

    @Field("country")
    private String country;

    @Field("hydrosphere_type")
    private String hydrosphereType;

    @Field("water_body_type_by_classification")
    private String waterBodyTypeByClassification;

    @Field("water_body_type_by_geographic")
    private String waterBodyTypeByGeographic;

    @Field("water_body_name")
    private String waterBodyName;

    @Field("water_body_type")
    private String waterBodyType;
    
    // 是否存在结果数据
    @Field("has_result")
    private String hasResult;
}
