package org.biosino.lf.mash.mashweb.util;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.csv.*;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @date 2025/8/1
 */
public class CsvReaderUtils {

    public static List<List<String>> readLinesToListStr(File file, Boolean containsHeader, char fieldSeparator) {
        if (!file.exists()) {
            throw new ServiceException("读取" + file.getAbsolutePath() + "文件不存在");
        }
        CsvReadConfig config = new CsvReadConfig();
        // containsHeader为true不读取第一行
        config.setContainsHeader(containsHeader);
        config.setFieldSeparator(fieldSeparator);
        config.setSkipEmptyRows(true);
        CsvReader reader = CsvUtil.getReader(config);
        CsvData read = reader.read(ResourceUtil.getUtf8Reader(file.getAbsolutePath()));
        List<CsvRow> rows = read.getRows();
        List<List<String>> dataList = new ArrayList<>();
        for (CsvRow row : rows) {
            List<String> strings = row.getRawList();
            dataList.add(strings);
        }
        return dataList;
    }

    public static List<List<String>> readLinesToListStr(File file) {
        return readLinesToListStr(file, true, ',');
    }

    public static List<List<String>> readLinesToListStr(File file, char fieldSeparator) {
        return readLinesToListStr(file, true, fieldSeparator);
    }
}
