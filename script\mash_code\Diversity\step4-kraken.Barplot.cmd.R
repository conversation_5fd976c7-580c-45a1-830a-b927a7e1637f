
suppressMessages(library(GetoptLong))
suppressMessages(library(ggplot2))
suppressMessages(library(tidyverse))
suppressMessages(library(plotly))
suppressMessages(library(htmlwidgets))
suppressMessages(library(reshape2))
rm(list = ls(all.names = TRUE))

# ✅ 添加 SelectDomain 和 SelectTaxonomy 参数
GetoptLong(
  "Input=s", "input Dir for Barplot Table, e.g., Barplot_Table",
  "Group=s", "group table, e.g., group.txt with two coloumn, and the header must be sample_name and group",
  "Output=s", "output Dir for Barplot figure, e.g., Barplot_Figure",
  "SelectDomain=s", "Specify one domain (e.g., B)",
  "SelectTaxonomy=s", "Specify one taxonomy level (e.g., G)",
  "verbose!", "print messages"
)

# 读取 group 表并标准化列名
group = read.csv(Group, sep = '\t', header = TRUE, row.names = 1, check.names = FALSE)
group$sample_name <- rownames(group)
names(group)[1] = 'Station'

# 颜色主题（保留不变）
color_theme = c(
  "#0288D1","#FF9800","#F44336","#26A69A","#FFCA28","#26C6DA",
  "#FFEE58","#009688","#8BC34A","#AB47BC","#CDDC39","#FFC107",
  "#E91E63","#9CCC65","#795548","#9C27B0","#3F51B5","#42A5F5",
  "#EF5350","#00BCD4","#66BB6A","#FF5722","#E6EE9C","#3F51B5",
  "#FFEB3B","#D4E157","#673AB7","#4CAF50","#EC407A","#9E9E9E"
)

if (!dir.exists(Output)) {
  dir.create(Output)
}

# ✅ 使用参数代替循环：只绘制一次
domain <- SelectDomain
taxonomy <- SelectTaxonomy
cat("Selected Domain:", domain, "\n")
cat("Selected Taxonomy:", taxonomy, "\n")

file_name <- paste0(Input, "/", "Barplot.", domain, ".", taxonomy, ".csv")
Barlegend_file_name <- paste0(Input, "/", "Barlegend.", domain, ".", taxonomy, ".csv")

if (file.exists(file_name) && file.exists(Barlegend_file_name)) {

  levels_taxonomy <- read.table(Barlegend_file_name, header = TRUE, sep = ",", quote = "",
                                 comment.char = "", stringsAsFactors = FALSE)[,'Tax']

  levels_sample_names <- read.csv(file_name, sep = ',', header = TRUE, stringsAsFactors = FALSE, check.names = FALSE) %>%
    {.[,'sample_name']} %>%
    unique()

  data <- read.csv(file_name, sep = ',', header = TRUE, stringsAsFactors = FALSE, check.names = FALSE) %>%
    mutate(sample_name = factor(sample_name, levels = levels_sample_names)) %>%
    mutate(Taxonomy = factor(Taxonomy, levels = levels_taxonomy)) %>%
    merge(group, by = 'sample_name', all.x = TRUE)

  num_sample <- length(levels_sample_names)
  cat("Number of samples:", num_sample, "\n")

  figure_width <- 24 + 0.1 * num_sample
  if (figure_width > 49) figure_width <- 49

  figure_vjust <- 1.5 + 0.005 * num_sample
  if (figure_vjust > 2) figure_vjust <- 2

  figure_text_size <- 15 - num_sample / 20
  if (figure_text_size < 5) figure_text_size <- 5

  p <- ggplot(data, aes(x = sample_name, y = Abudance, fill = Taxonomy)) +
    geom_bar(stat = "identity", width = 0.7, position = position_stack(reverse = TRUE)) +
    scale_fill_manual(values = color_theme) +
    facet_grid(. ~ Station, scales = 'free_x') +
    theme(
      plot.title = element_text(hjust = 0.5),
      title = element_text(family = 'serif', size = 20),
      axis.text.x = element_text(angle = 45, hjust = 1, vjust = figure_vjust, size = figure_text_size),
      axis.text.y = element_text(hjust = 1, size = figure_text_size),
      axis.ticks.x = element_blank(),
      legend.title = element_blank(),
      legend.position = 'bottom',
      legend.text = element_text(family = 'serif', size = 15),
      strip.text = element_text(size = 24),
      panel.background = element_rect(fill = NA),
      panel.grid = element_blank()
    ) +
    labs(x = NULL, y = 'Relative Abundance (%)') +
    guides(fill = guide_legend(ncol = 6, byrow = TRUE, reverse = FALSE, label.position = 'right'))

  ggsave(
    filename = paste0("Barplot.", domain, ".", taxonomy, ".pdf"),
    plot = p,
    path = Output,
    width = figure_width,
    height = 12
  )
} else {
  stop("File not found: ", file_name, " or ", Barlegend_file_name)
}

